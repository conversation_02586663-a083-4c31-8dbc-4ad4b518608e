import { z } from 'zod';

/**
 * Schema for creating a new user
 */
export const createUserSchema = z.object({
  firstName: z.string().min(1, 'First name is required'),
  lastName: z.string().min(1, 'Last name is required'),
  email: z.string().email('Invalid email address'),
  password: z
    .string()
    .min(8, 'Password must be at least 8 characters')
    .regex(/[A-Z]/, 'Password must contain at least one uppercase letter')
    .regex(/[a-z]/, 'Password must contain at least one lowercase letter')
    .regex(/\d/, 'Password must contain at least one number')
    .regex(/[^A-Z0-9]/i, 'Password must contain at least one special character'),
  roles: z.array(z.string()).min(1, 'At least one role is required'),
});

/**
 * Type for creating a new user
 */
export type CreateUserPayload = z.infer<typeof createUserSchema>;
