'use client';

import type { CreateUserPayload } from '../validation/team-member.validation';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useCallback } from 'react';
import { toast } from 'sonner';
import { createTeamMember as createTeamMemberApi } from '../services/team-member.service';

/**
 * Hook for creating a new team member
 *
 * This hook provides a way to create a new team member.
 * It uses React Query's useMutation for data mutation.
 *
 * @returns Create mutation and helper method
 */
export function useTeamMemberCreate() {
  // Get query client for cache invalidation
  const queryClient = useQueryClient();

  // Create team member mutation
  const createTeamMemberMutation = useMutation({
    mutationFn: (data: CreateUserPayload) => createTeamMemberApi(data),
    onSuccess: () => {
      // Invalidate all team member-related queries to refetch data
      queryClient.invalidateQueries({ queryKey: ['team-members'] });
      toast.success('Team member created successfully');
    },
    onError: (error: any) => {
      console.error('Error creating team member:', error);
      toast.error(error?.message || 'Failed to create team member');
    },
  });

  // Function to create a team member
  const createTeamMember = useCallback(async (data: CreateUserPayload) => {
    return createTeamMemberMutation.mutateAsync(data);
  }, [createTeamMemberMutation]);

  return {
    // Mutation state
    isCreating: createTeamMemberMutation.isPending,
    createError: createTeamMemberMutation.error,

    // Action
    createTeamMember,
  };
}
