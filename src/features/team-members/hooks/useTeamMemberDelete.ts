'use client';

import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useCallback } from 'react';
import { toast } from 'sonner';
import { deleteTeamMemberById } from '../services/team-member.service';

/**
 * Hook for deleting a team member
 *
 * This hook provides a way to delete a team member by ID.
 * It uses React Query's useMutation for data mutation.
 *
 * @returns Delete mutation and helper method
 */
export function useTeamMemberDelete() {
  // Get query client for cache invalidation
  const queryClient = useQueryClient();

  // Delete team member mutation
  const deleteTeamMemberMutation = useMutation({
    mutationFn: (id: string) => deleteTeamMemberById(id),
    onSuccess: () => {
      // Invalidate all team member-related queries to refetch data
      queryClient.invalidateQueries({ queryKey: ['team-members'] });
      toast.success('Team member deleted successfully');
    },
    onError: (error: any) => {
      console.error('Error deleting team member:', error);
      toast.error(error?.message || 'Failed to delete team member');
    },
  });

  // Function to delete a team member
  const deleteTeamMember = useCallback(async (id: string) => {
    return deleteTeamMemberMutation.mutateAsync(id);
  }, [deleteTeamMemberMutation]);

  return {
    // Mutation state
    isDeleting: deleteTeamMemberMutation.isPending,
    deleteError: deleteTeamMemberMutation.error,

    // Action
    deleteTeamMember,
  };
}
