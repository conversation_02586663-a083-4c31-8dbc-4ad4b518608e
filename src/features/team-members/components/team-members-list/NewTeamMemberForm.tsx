'use client';

import type { CreateUserPayload } from '../../validation/team-member.validation';
import Input from '@/shared/components/form/input/InputField';
import Label from '@/shared/components/form/Label';
import Select from '@/shared/components/form/Select';
import { Button } from '@/shared/components/ui/button';
import { Modal } from '@/shared/components/ui/modal';
import { useModal } from '@/shared/hooks/useModal';
import { zodResolver } from '@hookform/resolvers/zod';
import { useTranslations } from 'next-intl';
import { useCallback, useMemo } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { useTeamMemberCreate } from '../../hooks';
import { createUserSchema } from '../../validation/team-member.validation';

export function NewTeamMemberForm() {
  const t = useTranslations('TeamMember');
  const { isOpen, openModal, closeModal } = useModal();
  const { createTeamMember } = useTeamMemberCreate();

  // Default form values
  const defaultValues = useMemo(() =>
    (
      {
        firstName: '',
        lastName: '',
        email: '',
        password: '',
        roles: ['user'],
      }
    ), []);

  // Initialize React Hook Form with Zod validation
  const {
    control,
    handleSubmit,
    reset,
    formState: { errors, isSubmitting },
  } = useForm<CreateUserPayload>({
    resolver: zodResolver(createUserSchema),
    defaultValues,
  });

  // Reset form and close modal
  const resetForm = useCallback(() => {
    reset(defaultValues);
  }, [reset, defaultValues]);

  // Close modal and reset form
  const closeModalForm = useCallback(() => {
    closeModal();
    resetForm();
  }, [closeModal, resetForm]);

  const onSubmit = async (data: CreateUserPayload) => {
    // Prepare the payload
    const userPayload: CreateUserPayload = {
      firstName: data.firstName,
      lastName: data.lastName,
      email: data.email,
      password: data.password,
      roles: data.roles,
    };

    try {
      await createTeamMember(userPayload);
      resetForm();
      closeModalForm();
    } catch (error) {
      // Error is handled by the mutation hook
      console.error('Failed to create user:', error);
    }
  };

  const roleOptions = [
    { value: 'user', label: t('role_options.user') },
    { value: 'admin', label: t('role_options.admin') },
  ];

  return (
    <>
      <Button onClick={openModal}>{t('create_new_user_button')}</Button>

      <Modal isOpen={isOpen} onClose={closeModalForm} className="max-w-[700px] m-4">
        <div className="no-scrollbar relative w-full max-w-[700px] rounded-3xl bg-white p-6 dark:bg-gray-900 overflow-hidden">
          <div className="px-2 pr-14">
            <h4 className="mb-2 text-2xl font-medium text-gray-800 dark:text-white/90">
              {t('form_title')}
            </h4>
            <p className="mb-6 text-sm text-gray-500 dark:text-gray-400 lg:mb-7">
              {t('form_subtitle')}
            </p>
          </div>

          <form onSubmit={handleSubmit(onSubmit)} className="flex flex-col">
            <div className="custom-scrollbar h-[450px] overflow-y-auto px-2 pb-3">
              <div className="grid grid-cols-1 gap-x-6 gap-y-5 lg:grid-cols-2">
                {/* User Information Section */}
                <div className="col-span-2 mb-2">
                  <h5 className="text-lg font-medium text-gray-700 dark:text-gray-300">
                    {t('user_information')}
                  </h5>
                </div>

                {/* First Name */}
                <div className="col-span-1">
                  <Label htmlFor="firstName">
                    {t('first_name')}
                    {' '}
                    <span className="text-error-500">*</span>
                  </Label>
                  <Controller
                    control={control}
                    name="firstName"
                    render={({ field }) => (
                      <Input
                        id="firstName"
                        {...field}
                        placeholder={t('placeholder.first_name')}
                        type="text"
                        error={!!errors.firstName}
                        hint={errors.firstName?.message}
                      />
                    )}
                  />
                </div>

                {/* Last Name */}
                <div className="col-span-1">
                  <Label htmlFor="lastName">
                    {t('last_name')}
                    {' '}
                    <span className="text-error-500">*</span>
                  </Label>
                  <Controller
                    control={control}
                    name="lastName"
                    render={({ field }) => (
                      <Input
                        id="lastName"
                        {...field}
                        placeholder={t('placeholder.last_name')}
                        type="text"
                        error={!!errors.lastName}
                        hint={errors.lastName?.message}
                      />
                    )}
                  />
                </div>

                {/* Email */}
                <div className="col-span-1">
                  <Label htmlFor="email">
                    {t('email')}
                    {' '}
                    <span className="text-error-500">*</span>
                  </Label>
                  <Controller
                    control={control}
                    name="email"
                    render={({ field }) => (
                      <Input
                        id="email"
                        {...field}
                        placeholder={t('placeholder.email')}
                        type="email"
                        error={!!errors.email}
                        hint={errors.email?.message}
                      />
                    )}
                  />
                </div>

                {/* Password */}
                <div className="col-span-1">
                  <Label htmlFor="password">
                    {t('password')}
                    {' '}
                    <span className="text-error-500">*</span>
                  </Label>
                  <Controller
                    control={control}
                    name="password"
                    render={({ field }) => (
                      <Input
                        id="password"
                        {...field}
                        placeholder={t('placeholder.password')}
                        type="password"
                        error={!!errors.password}
                        hint={errors.password?.message}
                      />
                    )}
                  />
                </div>

                {/* Roles */}
                <div className="col-span-2">
                  <Label htmlFor="roles">
                    {t('roles')}
                    {' '}
                    <span className="text-error-500">*</span>
                  </Label>
                  <Controller
                    control={control}
                    name="roles"
                    render={({ field }) => (
                      <Select
                        options={roleOptions}
                        defaultValue={field.value.toString()}
                        onChange={value => field.onChange([value])}
                        placeholder={t('placeholder.roles')}
                        className="w-full"
                      />
                    )}
                  />
                </div>
              </div>
            </div>

            <div className="flex items-center gap-3 px-2 mt-6 lg:justify-end">
              <Button variant="outline" onClick={closeModalForm} disabled={isSubmitting}>
                {t('cancel_button')}
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? t('creating_button') : t('create_button')}
              </Button>
            </div>
          </form>
        </div>
      </Modal>
    </>
  );
}
