'use client';

import { DeleteConfirmationModal as SharedDeleteConfirmationModal } from '@/shared/components/modals/DeleteConfirmationModal';
import { useTranslations } from 'next-intl';
import { useTeamMemberDelete } from '../../hooks';

type TeamMemberDeleteConfirmationModalProps = {
  teamMemberId: string;
  teamMemberName: string;
};

/**
 * DeleteConfirmationModal Component
 *
 * This component uses the shared DeleteConfirmationModal for deleting a team member.
 */
export function DeleteConfirmationModal({
  teamMemberId,
  teamMemberName,
}: TeamMemberDeleteConfirmationModalProps) {
  const t = useTranslations('TeamMember');
  const { deleteTeamMember, isDeleting } = useTeamMemberDelete();

  // Wrap the deleteTeamMember function to match the expected type
  const handleDelete = async (id: string) => {
    await deleteTeamMember(id);
  };

  // Create a message with the team member name inserted
  const message = t.rich('confirm_delete_user', {
    name: () => <strong>{teamMemberName}</strong>,
  });

  return (
    <SharedDeleteConfirmationModal
      entityId={teamMemberId}
      entityName={teamMemberName}
      title={t('confirm_delete_user_title')}
      confirmationMessage={message}
      deleteButtonText={t('delete')}
      deletingButtonText={t('deleting')}
      cancelButtonText={t('cancel')}
      onDelete={handleDelete}
      isDeleting={isDeleting}
    />
  );
}
