import { z } from 'zod';
import { ProjectCampaignEnum, ProjectStatusEnum, ProjectTypeEnum } from '../types/project';

// Validation schema for creating a new project
export const createProjectSchema = z.object({
  clientName: z.string().min(1, 'Client name is required'),
  address: z.string().min(1, 'Address is required'),
  taxCode: z.string().min(1, 'Tax code is required'),
  contactPerson: z.string().min(1, 'Contact person is required'),
  tel: z.string().min(1, 'Telephone number is required'),
  email: z.string().email('Invalid email address'),
  industry: z.string().optional(),
  type: z.nativeEnum(ProjectTypeEnum),
  name: z.string().min(1, 'Project name is required'),
  campaign: z.nativeEnum(ProjectCampaignEnum),
  description: z.string().optional(),
  startDate: z.string(),
  endDate: z.string().optional(),
  status: z.nativeEnum(ProjectStatusEnum).optional(),
  ownedId: z.string().optional(),
  memberIds: z.array(z.string()).optional(),
});

export type CreateProjectPayload = z.infer<typeof createProjectSchema>;
