import { create } from 'zustand';

// Define the store type for chat box visibility
type ChatBoxVisibilityStore = {
  isVisible: boolean;
  show: () => void;
  hide: () => void;
  toggle: () => void;
};

// Create a global store for chat box visibility
// This allows the state to be shared across components without prop drilling
export const useChatBoxStore = create<ChatBoxVisibilityStore>(set => ({
  isVisible: false,
  show: () => set({ isVisible: true }),
  hide: () => set({ isVisible: false }),
  toggle: () => set(state => ({ isVisible: !state.isVisible })),
}));
