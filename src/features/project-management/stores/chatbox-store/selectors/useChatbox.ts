import { useChatBoxStore } from '../initChatboxStore';

/**
 * Hook for accessing chatbox visibility state only
 *
 * This atomic selector only subscribes to the isVisible property,
 * preventing unnecessary re-renders when actions change.
 *
 * @returns Boolean indicating if chatbox is visible
 */
export function useChatBoxVisible() {
  return useChatBoxStore(state => state.isVisible);
}

/**
 * Hook for accessing chatbox show action
 *
 * @returns show function
 */
export function useChatBoxShow() {
  return useChatBoxStore(state => state.show);
}

/**
 * Hook for accessing chatbox hide action
 *
 * @returns hide function
 */
export function useChatBoxHide() {
  return useChatBoxStore(state => state.hide);
}

/**
 * Hook for accessing chatbox toggle action
 *
 * @returns toggle function
 */
export function useChatBoxToggle() {
  return useChatBoxStore(state => state.toggle);
}
