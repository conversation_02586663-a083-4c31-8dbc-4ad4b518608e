import type { ProjectStoreStateCreator, ProjectWorkflowSelectionSlice } from '../types';

/**
 * Creates the Project Selection slice
 *
 * This slice handles project and step selection state.
 * It manages which project and step are currently selected and provides
 * actions to update the selection.
 *
 * @param set - Zustand set function
 * @param _get - Zustand get function (unused)
 * @returns ProjectSelectionSlice
 */
export const createProjectDetailSlice: ProjectStoreStateCreator<ProjectWorkflowSelectionSlice> = (set, _get) => ({
  // ============================================================================
  // STATE
  // ============================================================================

  selectedProjectId: null,
  selectedStepId: null,

  // ============================================================================
  // ACTIONS
  // ============================================================================

  actions: {

    /**
     * Set the selected project ID
     *
     * When a new project is selected, we typically want to clear the step selection
     * since steps are project-specific.
     *
     * @param id - The project ID to select, or null to clear selection
     */
    setSelectedProjectId: (id) => {
      set(state => ({
        selectedProjectId: id,
        // Clear step selection when project changes (unless it's the same project)
        selectedStepId: state.selectedProjectId === id ? state.selectedStepId : null,
      }));
    },

    /**
     * Set the selected step ID
     *
     * @param id - The step ID to select, or null to clear selection
     */
    setSelectedStepId: (id) => {
      set({ selectedStepId: id });
    },

    /**
     * Clear both project and step selection
     *
     * This is useful when navigating away from project-specific views
     * or when resetting the application state.
     */
    clearSelection: () => {
      set({
        selectedProjectId: null,
        selectedStepId: null,
      });
    },
  },
});
