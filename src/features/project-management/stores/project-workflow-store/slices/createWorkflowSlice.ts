import type {
  NavigationTarget,
  WorkflowStep,
  // Step as WorkflowStep,
  //  Task as WorkflowTask
} from '../../../types/workflow';
import type { ProjectStoreStateCreator, WorkflowSlice } from '../types';
import { EStatusTask } from '../../../types/workflow';
import {
  autoStartFirstTask,
  calculateNextTarget,
  calculatePreviousTarget,
  createNavigationCache,
  createWorkflowMaps,
  findFirstActiveTaskAndStep,
  getStepById,
  saveCurrentStepInLocalStorage,
  updateStepInTask,
  updateTaskInWorkflow,
} from '@/features/project-management/utils/workflowUtils';

export const createWorkflowSlice: ProjectStoreStateCreator<WorkflowSlice> = (set, get) => {
  // Navigation cache instance
  const navCache = createNavigationCache();

  // Helper to get cached navigation result
  const getCachedNavigation = (
    direction: 'next' | 'prev',
    currentTask: WorkflowStep, // task is a collection of steps (1 task might have multiple steps/sub-tasks)
    currentStep: WorkflowStep | null,
    workflow: WorkflowStep[],
  ): NavigationTarget | null => {
    const cached = navCache.get(direction, currentTask.id, currentStep?.id);

    if (cached !== undefined) {
      return cached;
    }

    const maps = createWorkflowMaps(workflow);
    const result = direction === 'next'
      ? calculateNextTarget(currentTask, currentStep, maps)
      : calculatePreviousTarget(currentTask, currentStep, maps);

    navCache.set(direction, currentTask.id, currentStep?.id, result);
    return result;
  };

  return {
    // ============================================================================
    // STATE
    // ============================================================================
    workflow: [],
    currentTask: null,
    currentStep: null,
    isLoading: false,
    error: null,
    projectName: '',
    currentStepInfoIds: '',

    actions: {
      // ============================================================================
      // INITIALIZATION
      // ============================================================================
      initializeWorkflow: (workflowData) => {
        set(() => {
          const { task: firstTask, step: firstStep } = findFirstActiveTaskAndStep(workflowData);
          const updatedWorkflow = autoStartFirstTask(workflowData);

          // Clear navigation cache when workflow changes
          navCache.clear();
          return {
            workflow: updatedWorkflow,
            currentTask: firstTask,
            currentStep: firstStep,
            isLoading: false,
            error: null,
          };
        });
      },

      // ============================================================================
      // STEP OPERATIONS
      // ============================================================================
      completeStep: (stepId, data = null) => {
        set((state) => {
          if (!state.workflow.length || !state.currentTask) {
            return state;
          }

          const maps = createWorkflowMaps(state.workflow);
          let updatedWorkflow = [...state.workflow];

          // Complete the current step/task
          if (state.currentTask.children.length === 0 && state.currentTask.id === stepId) {
            // Task without steps
            updatedWorkflow = updateTaskInWorkflow(updatedWorkflow, stepId, task => ({
              ...task,
              status: EStatusTask.COMPLETED,
            }));
          } else {
            // Task with steps
            const stepInfo = maps.stepMap.get(stepId);
            if (stepInfo) {
              updatedWorkflow = updateTaskInWorkflow(updatedWorkflow, stepInfo.task.id, task =>
                updateStepInTask(task, stepId, step => ({
                  ...step,
                  status: EStatusTask.COMPLETED,
                  data: data ? { ...step.data, ...data } : step.data,
                })));
            }
          }

          // Find and setup next target
          const nextTarget = getCachedNavigation('next', state.currentTask, state.currentStep, state.workflow);
          let nextTask: WorkflowStep | null = null;
          let nextStep: WorkflowStep | null = null;
          if (nextTarget) {
            updatedWorkflow = updateTaskInWorkflow(updatedWorkflow, nextTarget.taskId, (task) => {
              const updatedTask = { ...task, status: task.status === EStatusTask.COMPLETED ? task.status : EStatusTask.IN_PROGRESS };

              if (nextTarget.stepId) {
                updatedTask.children = task.children.map(step =>
                  step.id === nextTarget.stepId
                    ? { ...step, status: step.status === EStatusTask.COMPLETED ? step.status : EStatusTask.IN_PROGRESS }
                    : step,
                );
                nextStep = updatedTask.children.find(s => s.id === nextTarget.stepId) || null;
              }

              nextTask = updatedTask;
              return updatedTask;
            });
          }

          // Clear cache after workflow changes
          navCache.clear();
          return {
            workflow: updatedWorkflow,
            currentTask: nextTask || state.currentTask,
            currentStep: nextStep,
          };
        });
      },

      updateStepData: (stepId, data) => {
        set((state) => {
          if (!state.workflow.length) {
            return state;
          }

          const taskWithStep = state.workflow.find(task =>
            task.children.some(step => step.id === stepId),
          );

          if (!taskWithStep) {
            return state;
          }

          const updatedWorkflow = updateTaskInWorkflow(state.workflow, taskWithStep.id, task =>
            updateStepInTask(task, stepId, step => ({
              ...step,
              data: { ...step.data, ...data },
            })));

          // Update current step if it matches
          const updatedCurrentStep = state.currentStep?.id === stepId
            ? getStepById(stepId, createWorkflowMaps(updatedWorkflow))
            : state.currentStep;

          return {
            workflow: updatedWorkflow,
            currentStep: updatedCurrentStep,
          };
        });
      },

      initializeStepData: (stepId) => {
        set((state) => {
          if (!state.workflow.length) {
            return state;
          }

          const maps = createWorkflowMaps(state.workflow);
          const stepInfo = maps.stepMap.get(stepId);

          if (!stepInfo || stepInfo.step.data) {
            return state;
          } // Already has data

          const updatedWorkflow = updateTaskInWorkflow(state.workflow, stepInfo.task.id, task =>
            updateStepInTask(task, stepId, step => ({
              ...step,
              data: {},
            })));

          return { workflow: updatedWorkflow };
        });
      },

      // ============================================================================
      // NAVIGATION
      // ============================================================================
      moveToNextStep: () => {
        set((state) => {
          if (!state.currentTask || !state.workflow.length) {
            return state;
          }

          const nextTarget = getCachedNavigation('next', state.currentTask, state.currentStep, state.workflow);

          if (!nextTarget) {
            return state;
          }

          let updatedWorkflow = [...state.workflow];

          // Complete current step/task
          if (state.currentTask.children.length === 0) {
            updatedWorkflow = updateTaskInWorkflow(updatedWorkflow, state.currentTask.id, task => ({
              ...task,
              status: EStatusTask.COMPLETED,
            }));
          } else if (state.currentStep) {
            updatedWorkflow = updateTaskInWorkflow(updatedWorkflow, state.currentTask.id, task =>
              updateStepInTask(task, state.currentStep!.id, step => ({
                ...step,
                status: EStatusTask.COMPLETED,
              })));
          }

          // Setup next target
          updatedWorkflow = updateTaskInWorkflow(updatedWorkflow, nextTarget.taskId, (task) => {
            const updatedTask = { ...task, status: task.status !== EStatusTask.COMPLETED ? EStatusTask.IN_PROGRESS : task.status };

            if (nextTarget.stepId) {
              updatedTask.children = task.children.map(step =>
                step.id === nextTarget.stepId
                  ? { ...step, status: step.status !== EStatusTask.COMPLETED ? EStatusTask.IN_PROGRESS : step.status }
                  : step,
              );
            }

            return updatedTask;
          });

          const nextTask = updatedWorkflow.find(t => t.id === nextTarget.taskId)!;
          const nextStep = nextTarget.stepId
            ? nextTask.children.find(s => s.id === nextTarget.stepId) || null
            : null;

          // Clear cache after workflow changes
          navCache.clear();
          saveCurrentStepInLocalStorage(nextTask.id, nextStep?.id ?? '');

          return {
            workflow: updatedWorkflow,
            currentTask: nextTask,
            currentStep: nextStep,
          };
        });
      },

      moveToPreviousStep: () => {
        set((state) => {
          if (!state.currentTask || !state.workflow.length) {
            return state;
          }

          const prevTarget = getCachedNavigation('prev', state.currentTask, state.currentStep, state.workflow);
          if (!prevTarget) {
            return state;
          }

          let updatedWorkflow = [...state.workflow];

          // Setup previous target
          updatedWorkflow = updateTaskInWorkflow(updatedWorkflow, prevTarget.taskId, (task) => {
            const updatedTask = { ...task, status: task.status !== EStatusTask.COMPLETED ? EStatusTask.IN_PROGRESS : task.status };

            if (prevTarget.stepId) {
              updatedTask.children = task.children.map(step =>
                step.id === prevTarget.stepId
                  ? { ...step, status: step.status !== EStatusTask.COMPLETED ? EStatusTask.IN_PROGRESS : step.status }
                  : step,
              );
            }

            return updatedTask;
          });

          const prevTask = updatedWorkflow.find(t => t.id === prevTarget.taskId)!;
          const prevStep = prevTarget.stepId
            ? prevTask.children.find(s => s.id === prevTarget.stepId) || null
            : null;

          // Clear cache after workflow changes
          navCache.clear();
          saveCurrentStepInLocalStorage(prevTask.id, prevStep?.id ?? '');

          return {
            workflow: updatedWorkflow,
            currentTask: prevTask,
            currentStep: prevStep,
          };
        });
      },

      // ============================================================================
      // UTILITIES
      // ============================================================================
      getStepById: (stepId) => {
        const { workflow } = get();
        if (!workflow.length) {
          return null;
        }

        const maps = createWorkflowMaps(workflow);
        return getStepById(stepId, maps);
      },

      updateQAListData: (stepId, qaList) => {
        set((state) => {
          if (!state.workflow.length) {
            return state;
          }

          const taskWithStep = state.workflow.find(task =>
            task.children.some(step => step.id === stepId),
          );

          if (!taskWithStep) {
            return state;
          }

          const updatedWorkflow = updateTaskInWorkflow(state.workflow, taskWithStep.id, task =>
            updateStepInTask(task, stepId, step => ({
              ...step,
              qaList: [...qaList],
            })));

          const updatedCurrentStep = state.currentStep?.id === stepId
            ? updatedWorkflow
              .find(t => t.id === taskWithStep.id)
              ?.children
              .find(s => s.id === stepId) || state.currentStep
            : state.currentStep;

          return {
            workflow: updatedWorkflow,
            currentStep: updatedCurrentStep,
          };
        });
      },

      updateStatus: (id, status, isTask = false) => {
        set((state) => {
          if (!state.workflow.length) {
            return state;
          }

          if (isTask) {
            const updatedWorkflow = updateTaskInWorkflow(state.workflow, id, task => ({
              ...task,
              status,
            }));

            const updatedCurrentTask = state.currentTask?.id === id
              ? updatedWorkflow.find(t => t.id === id) || state.currentTask
              : state.currentTask;

            return {
              workflow: updatedWorkflow,
              currentTask: updatedCurrentTask,
            };
          } else {
            // Update step status
            const taskWithStep = state.workflow.find(task =>
              task.children.some(step => step.id === id),
            );

            if (!taskWithStep) {
              return state;
            }

            const updatedWorkflow = updateTaskInWorkflow(state.workflow, taskWithStep.id, task =>
              updateStepInTask(task, id, step => ({
                ...step,
                status,
              })));

            const updatedCurrentStep = state.currentStep?.id === id
              ? updatedWorkflow
                .find(t => t.id === taskWithStep.id)
                ?.children
                .find(s => s.id === id) || state.currentStep
              : state.currentStep;

            return {
              workflow: updatedWorkflow,
              currentStep: updatedCurrentStep,
            };
          }
        });
      },

      // ============================================================================
      // SIMPLE SETTERS & UTILITIES
      // ============================================================================
      updateCurrentStepInfoIds: id => set({ currentStepInfoIds: id }),
      setProjectName: name => set({ projectName: name }),
      clearNavigationCache: () => navCache.clear(),

      // Expose calculation methods for external use
      calculateNextStep: (currentTask, currentStep, workflow) => {
        const maps = createWorkflowMaps(workflow);
        return calculateNextTarget(currentTask, currentStep, maps);
      },

      calculatePreviousStep: (currentTask, currentStep, workflow) => {
        const maps = createWorkflowMaps(workflow);
        return calculatePreviousTarget(currentTask, currentStep, maps);
      },

      getNextStepId: () => {
        const { currentTask, currentStep, workflow } = get();
        if (!currentTask || !workflow.length) {
          return '';
        }

        const nextTarget = getCachedNavigation('next', currentTask, currentStep, workflow);
        return nextTarget?.stepId || '';
      },

      moveToCurrentStep: (currentTaskId: string, currentStepId: string) => {
        set((state) => {
          if (!state.currentTask || !state.workflow.length) {
            return state;
          }
          const updatedWorkflow = [...state.workflow];
          const currentTask = updatedWorkflow.find(t => t.id === currentTaskId)!;
          const currentStep
             = currentTask.children.find(t => t.id === currentStepId)! || null;
          return {
            ...state,
            currentTask,
            currentStep,
          };
        });
      },
    },
  };
};
