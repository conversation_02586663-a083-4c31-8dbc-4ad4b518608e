'use client';

import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useCallback } from 'react';
import { toast } from 'sonner';
import { deleteProjectById } from '../services/project.service';

/**
 * Hook for deleting a project
 *
 * This hook provides a way to delete a project by ID.
 * It uses React Query's useMutation for data mutation.
 *
 * @returns Delete mutation and helper method
 */
export function useProjectDelete() {
  // Get query client for cache invalidation
  const queryClient = useQueryClient();

  // Delete project mutation
  const deleteProjectMutation = useMutation({
    mutationFn: (id: string) => deleteProjectById(id),
    onSuccess: () => {
      // Invalidate all project-related queries to refetch data
      queryClient.invalidateQueries({ queryKey: ['projects'] });
      toast.success('Project deleted successfully');
    },
    onError: (error) => {
      console.error('Error deleting project:', error);
      toast.error('Failed to delete project');
    },
  });

  // Function to delete a project
  const deleteProject = useCallback(async (id: string) => {
    return deleteProjectMutation.mutateAsync(id);
  }, [deleteProjectMutation]);

  return {
    // Mutation state
    isDeleting: deleteProjectMutation.isPending,
    deleteError: deleteProjectMutation.error,

    // Action
    deleteProject,
  };
}
