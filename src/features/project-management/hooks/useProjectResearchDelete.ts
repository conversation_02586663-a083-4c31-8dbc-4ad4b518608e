'use client';

import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useCallback } from 'react';
import { toast } from 'sonner';
import { deleteResearch } from '../services/project.service';

/**
 * Hook for deleting a research item
 *
 * This hook provides a way to delete a research item by ID.
 * It uses React Query's useMutation for data mutation.
 *
 * @returns Delete mutation and helper method
 */
export function useProjectResearchDelete() {
  // Get query client for cache invalidation
  const queryClient = useQueryClient();

  // Delete research mutation
  const deleteResearchMutation = useMutation({
    mutationFn: (id: string) => deleteResearch(id),
    onSuccess: () => {
      // Invalidate all research-related queries to refetch data
      queryClient.invalidateQueries({ queryKey: ['research'] });
      toast.success('Research deleted successfully');
    },
    onError: (error) => {
      console.error('Error deleting research:', error);
      toast.error('Failed to delete research');
    },
  });

  // Function to delete a research item
  const deleteResearchItem = useCallback(async (id: string) => {
    return deleteResearchMutation.mutateAsync(id);
  }, [deleteResearchMutation]);

  return {
    // Mutation state
    isDeleting: deleteResearchMutation.isPending,
    deleteError: deleteResearchMutation.error,

    // Action
    deleteResearchItem,
  };
}
