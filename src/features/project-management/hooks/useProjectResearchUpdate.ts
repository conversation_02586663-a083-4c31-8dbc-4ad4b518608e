'use client';

import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useCallback } from 'react';
import { toast } from 'sonner';
import { updateResearch } from '../services/project.service';
import type { UpdateProjectResearch } from '../types/research';

/**
 * Hook for updating a research item
 *
 * This hook provides a way to update a research item by ID.
 * It uses React Query's useMutation for data mutation.
 *
 * @returns Update mutation and helper method
 */
export function useProjectResearchUpdate() {
  // Get query client for cache invalidation
  const queryClient = useQueryClient();

  // Update research mutation
  const updateResearchMutation = useMutation({
    mutationFn: ({ id, data }: { id: string; data: Partial<UpdateProjectResearch> }) =>
      updateResearch(id, data),
    onSuccess: () => {
      // Invalidate all research-related queries to refetch data
      queryClient.invalidateQueries({ queryKey: ['research'] });
      toast.success('Research updated successfully');
    },
    onError: (error) => {
      console.error('Error updating research:', error);
      toast.error('Failed to update research');
    },
  });

  // Function to update a research item
  const updateResearchItem = useCallback(async (id: string, data: Partial<UpdateProjectResearch>) => {
    return updateResearchMutation.mutateAsync({ id, data });
  }, [updateResearchMutation]);

  return {
    // Mutation state
    isUpdating: updateResearchMutation.isPending,
    updateError: updateResearchMutation.error,

    // Action
    updateResearchItem,
  };
}
