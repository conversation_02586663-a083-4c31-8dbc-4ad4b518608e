'use client';

import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useCallback } from 'react';
import { toast } from 'sonner';
import type { CreateProjectResearch } from '../types/research';
import { createProjectResearchApi } from '../services/project.service';

/**
 * Hook for creating a new project
 *
 * This hook provides a way to create a new project.
 * It uses React Query's useMutation for data mutation.
 *
 * @returns Create mutation and helper method
 */
export function useProjectResearchCreate() {
  // Get query client for cache invalidation
  const queryClient = useQueryClient();

  // Create project mutation
  const createProjectResearchMutation = useMutation({
    mutationFn: (data: CreateProjectResearch) => createProjectResearchApi(data),
    onSuccess: () => {
      // Invalidate all project-related queries to refetch data
      queryClient.invalidateQueries({ queryKey: ['researchs'] });
      toast.success('Research created successfully');
    },
    onError: (error) => {
      console.error('Error creating research:', error);
      toast.error('Failed to create research');
    },
  });

  // Function to create a project
  const createProjectResearch = useCallback(async (data: CreateProjectResearch) => {
    return createProjectResearchMutation.mutateAsync(data);
  }, [createProjectResearchMutation]);

  return {
    // Mutation state
    isCreating: createProjectResearchMutation.isPending,
    createError: createProjectResearchMutation.error,

    // Action
    createProjectResearch,
  };
}
