'use client';

import { DeleteConfirmationModal as SharedDeleteConfirmationModal } from '@/shared/components/modals/DeleteConfirmationModal';
import { useTranslations } from 'next-intl';
import { useProjectDelete } from '../../../hooks';

type ProjectDeleteConfirmationModalProps = {
  projectId: string;
  projectName: string;
  /**
   * Optional callback to be called after the modal is closed
   */
  onClose?: () => void;
};

/**
 * ProjectDeleteConfirmationModal Component
 *
 * This component uses the shared DeleteConfirmationModal for deleting a project.
 */
export function ProjectDeleteConfirmationModal({
  projectId,
  projectName,
  onClose,
}: ProjectDeleteConfirmationModalProps) {
  const t = useTranslations('Project');
  const { deleteProject, isDeleting } = useProjectDelete();

  // Wrap the deleteProject function to match the expected type
  const handleDelete = async (id: string) => {
    await deleteProject(id);
    if (onClose) {
      onClose();
    }
  };

  // Create a message with the project name inserted
  const message = t.rich('confirm_delete_project', {
    name: () => <strong>{projectName}</strong>,
  });

  return (
    <SharedDeleteConfirmationModal
      entityId={projectId}
      entityName={projectName}
      title={t('confirm_delete_project_title', { fallback: 'Delete Project' })}
      confirmationMessage={message}
      deleteButtonText={t('delete', { fallback: 'Delete' })}
      deletingButtonText={t('deleting', { fallback: 'Deleting...' })}
      cancelButtonText={t('cancel', { fallback: 'Cancel' })}
      onDelete={handleDelete}
      isDeleting={isDeleting}
    />
  );
}
