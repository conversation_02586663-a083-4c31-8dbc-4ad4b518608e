import { cva } from 'class-variance-authority';
import { ProjectStatusEnum, ProjectTypeEnum } from '../../../types/project';

/**
 * Project Card Container Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The project card container variants are defined using the cva utility for consistent styling.
 */
export const projectCardContainerVariants = cva(
  'relative group hover:shadow-md hover:translate-y-[-2px] transition-all duration-300 rounded-lg',
  {
    variants: {
      variant: {
        default: '',
        featured: 'ring-2 ring-primary/20',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
);

/**
 * Project Card Status Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The project card status variants are defined using the cva utility for consistent styling.
 */
export const projectCardStatusVariants = cva(
  'text-xs font-medium px-2.5 py-1 rounded-full',
  {
    variants: {
      status: {
        [ProjectStatusEnum.PLANNED]: 'bg-warning-100 text-warning-800 border-warning-300 dark:bg-warning-800/20 dark:text-warning-500 dark:border-warning-800/50',
        [ProjectStatusEnum.IN_PROGRESS]: 'bg-info-100 text-info-800 border-info-300 dark:bg-info-800/20 dark:text-info-500 dark:border-info-800/50',
        [ProjectStatusEnum.COMPLETED]: 'bg-success-100 text-success-800 border-success-300 dark:bg-success-800/20 dark:text-success-500 dark:border-success-800/50',
        [ProjectStatusEnum.ON_HOLD]: 'bg-muted text-muted-foreground border-muted dark:bg-muted/20 dark:text-muted-foreground dark:border-muted/50',
      },
    },
    defaultVariants: {
      status: ProjectStatusEnum.PLANNED,
    },
  },
);

/**
 * Project Card Category Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The project card category variants are defined using the cva utility for consistent styling.
 */
export const projectCardCategoryVariants = cva(
  'text-xs',
  {
    variants: {
      type: {
        [ProjectTypeEnum.BRANDING]: 'text-blue-500 dark:text-blue-400',
        [ProjectTypeEnum.GENERAL_CONSULTING]: 'text-brand-500 dark:text-brand-400',
        [ProjectTypeEnum.DIAGNOSTICS]: 'text-success-500 dark:text-success-400',
      },
    },
    defaultVariants: {
      type: ProjectTypeEnum.BRANDING,
    },
  },
);

/**
 * Project Card Progress Bar Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The project card progress bar variants are defined using the cva utility for consistent styling.
 */
export const projectCardProgressBarVariants = cva(
  'w-full bg-muted rounded-full h-[6px]',
  {
    variants: {
      variant: {
        default: '',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
);

/**
 * Project Card Progress Fill Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The project card progress fill variants are defined using the cva utility for consistent styling.
 */
export const projectCardProgressFillVariants = cva(
  'h-[6px] rounded-full transition-all duration-500 ease-out',
  {
    variants: {
      status: {
        [ProjectStatusEnum.PLANNED]: 'bg-primary',
        [ProjectStatusEnum.IN_PROGRESS]: 'bg-primary',
        [ProjectStatusEnum.COMPLETED]: 'bg-primary',
        [ProjectStatusEnum.ON_HOLD]: 'bg-primary',
      },
    },
    defaultVariants: {
      status: ProjectStatusEnum.PLANNED,
    },
  },
);

/**
 * Project Card Metadata Container Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The project card metadata container variants are defined using the cva utility for consistent styling.
 */
export const projectCardMetadataContainerVariants = cva(
  'grid grid-cols-2 gap-3 text-xs pt-4 border-t',
  {
    variants: {
      variant: {
        default: 'border-border',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
);

/**
 * Project Card Metadata Item Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The project card metadata item variants are defined using the cva utility for consistent styling.
 */
export const projectCardMetadataItemVariants = cva(
  'flex items-center gap-2',
  {
    variants: {
      variant: {
        default: 'text-muted-foreground',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
);

/**
 * Project Card Icon Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The project card icon variants are defined using the cva utility for consistent styling.
 */
export const projectCardIconVariants = cva(
  'h-3.5 w-3.5 flex-shrink-0',
  {
    variants: {
      variant: {
        default: 'text-muted-foreground/70',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
);
