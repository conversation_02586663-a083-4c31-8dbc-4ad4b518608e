'use client';

import type { VariantProps } from 'class-variance-authority';
import type { Project } from '../../types/project';
import { AlertCircle } from 'lucide-react';
import { useEffect } from 'react';
import { useInView } from 'react-intersection-observer';
import { But<PERSON> } from '@/shared/components/ui/button';
import { cn } from '@/shared/utils/utils';
import { useProjectInfiniteQuery } from '../../hooks';
import ProjectCard from './project-card/ProjectCard';
import ProjectListHeader from './project-list-header/ProjectListHeader';
import {
  projectListContainerVariants,
  projectListEmptyMessageVariants,
  projectListErrorContainerVariants,
  projectListErrorIconVariants,
  projectListErrorMessageVariants,
  projectListLoadingMessageVariants,
} from './project-list-variants';
import ProjectCardSkeleton from './ProjectCardSkeleton';

type ProjectListProps = {
  variant?: VariantProps<typeof projectListContainerVariants>['variant'];
};

/**
 * ProjectList Component
 *
 * This component uses semantic color tokens from our theming system.
 * It displays a list of projects with filtering and infinite scrolling.
 */
export default function ProjectList({ variant = 'default' }: ProjectListProps = {}) {
  // Set up query with current filters
  const {
    infiniteData: data,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    isLoading,
    error,
    refetch,
  } = useProjectInfiniteQuery();

  // Force a re-render if the data is available but not being displayed
  useEffect(() => {
    if (data && data.pages && data.pages.length > 0) {
      // This is just to trigger a re-render
      const timer = setTimeout(() => {}, 0);
      return () => clearTimeout(timer);
    }
    return undefined;
  }, [data]);

  // Check if there's an error
  const isError = !!error;

  // Set up intersection observer for infinite scrolling
  const { ref, inView } = useInView();

  // Load more projects when the user scrolls to the bottom
  useEffect(() => {
    if (inView && hasNextPage && !isFetchingNextPage) {
      fetchNextPage();
    }
  }, [inView, hasNextPage, isFetchingNextPage, fetchNextPage]);

  // Safely flatten all projects from all pages with null checks
  const allProjects = data?.pages
    ? data.pages.flatMap((page) => {
        // The API response is in the data property of the ApiResponse
        // The items array is directly in the data property
        return page?.data?.items || [];
      })
    : [];

  // Determine how many skeleton cards to show
  const skeletonCount = (isLoading || isFetchingNextPage) ? 4 : 0;

  return (
    <div className={cn(projectListContainerVariants({ variant }))}>
      {/* Fixed header with filters - doesn't scroll */}
      <ProjectListHeader isLoading={isLoading || isFetchingNextPage} />

      {/* Scrollable content area */}
      <div className="flex-1 overflow-y-auto px-4 py-4">
        {isError
          ? (
              <div className={cn(projectListErrorContainerVariants({ variant }))}>
                <AlertCircle className={cn(projectListErrorIconVariants({ variant }))} />
                <p className={cn(projectListErrorMessageVariants({ variant }))}>
                  {error?.message || 'An error occurred while loading projects.'}
                </p>
                <Button onClick={() => refetch()} variant="outline">
                  Try Again
                </Button>
              </div>
            )
          : isLoading
            ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-4">
                  {Array.from({ length: 4 }).map((_, index) => (
                    <ProjectCardSkeleton key={index} />
                  ))}
                </div>
              )
            : allProjects.length === 0
              ? (
                  <div className={cn(projectListEmptyMessageVariants({ variant }))}>
                    <p>No projects found.</p>
                  </div>
                )
              : (
                  <>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-4">
                      {allProjects.map((project: Project) => (
                        <ProjectCard key={project.id} project={project} />
                      ))}

                      {/* Loading more indicator */}
                      {skeletonCount > 0
                        && Array.from({ length: skeletonCount }).map((_, index) => (
                          <ProjectCardSkeleton key={`skeleton-${index}`} />
                        ))}
                    </div>

                    {/* Intersection observer target */}
                    {hasNextPage && (
                      <div
                        ref={ref}
                        className="h-20 flex items-center justify-center mt-4"
                      >
                        {isFetchingNextPage && (
                          <div className={cn(projectListLoadingMessageVariants({ variant }))}>
                            Loading more projects...
                          </div>
                        )}
                      </div>
                    )}
                  </>
                )}
      </div>
    </div>
  );
}
