import React, { useEffect, useRef, useState } from 'react';
import ProjectCardSkeleton from '../../project-list/ProjectCardSkeleton';
import QualityView from './QualityView';
import { useCurrentStep, useCurrentTask, useWorkflowActions, useWorkflowTasks } from '@/features/project-management/stores/project-workflow-store';
import type { documentFileUpload, ProjectCampaignEnum, TemplateFiles } from '@/features/project-management/types/project';
import { useGetInfoDetail } from '@/features/project-management/hooks';
import { useGetListTemplates } from '@/features/project-management/hooks/useProjectTemplate';
import { useParams } from 'next/navigation';
import { getFile } from '@/features/project-management/utils/initialScreeningUtils';
import { copilotkitAction } from '@/actions/copilotkit/copilotkit-actions';
import { EEndpointApiCopilotkit } from '@/shared/enums/global';
import type { QuestionnaireResponse } from '@/features/questionnaire/types/questionnaire';
import { updateNameFieldForQuestionnaire } from '@/features/project-management/utils/workflowUtils';
import QuantityQuestionnaire from './QuanityQuestionnaire';
import { EOptionSelect } from '@/features/project-management/types/questionnaire';
import type { QuestionnaireType, toggleOptionsType } from '@/features/project-management/types/questionnaire';
import { Button } from '@/shared/components/ui/button';
import { CheckBadgeIcon, CheckIcon, LinkIcon, SendIcon } from '@/shared/icons';
import { useProjectUpdateQuestionAnswer } from '@/features/project-management/hooks/useProjectUpdateQuestionAnswer';
import { useUpdateStatusStep } from '@/features/project-management/hooks/useUpdateStatusStep';
import { EStatusTask } from '@/features/project-management/types/workflow';
import type { QuestionnaireFormTypeRef } from '@/features/questionnaire/components/layouts/QuestionnaireForm';

const GeneratedQuestionnaire: React.FC = () => {
  const toggleOptions = [
    { id: 'quantitative' as QuestionnaireType, label: 'Quantitative Questionnaire', type: EOptionSelect.FORM },
    { id: 'qualitative' as QuestionnaireType, label: 'Qualitative Questionnaire', type: EOptionSelect.MARKDOWN },
  ];

  const [isLoading, setIsLoading] = useState(true);

  const [selectedType, setSelectedType] = useState<QuestionnaireType>('quantitative');

  const [campaignSelected, setCampaignSelected] = useState<ProjectCampaignEnum | null>(null);

  const [templateFile, setTemplateFile] = useState<TemplateFiles[]>([]);

  const [quality, setQuality] = useState<string>('');

  const [quantity, setQuantity] = useState<QuestionnaireResponse | null>(null);

  const [optionList, setOptionList] = useState<toggleOptionsType[]>(toggleOptions);

  const [isSaved, setIsSaved] = useState<boolean>(false);

  const [isCopied, setIsCopied] = useState<boolean>(false);

  const [analysisBrief, setAnalysisBrief] = useState<string>('');

  const currentStep = useCurrentStep();

  const currentTask = useCurrentTask();

  const currentStepId = currentStep?.id;

  const workflow = useWorkflowTasks();

  const idSecondStep = workflow[1]?.steps[0]?.id;

  const idSOW = workflow[2]?.steps[1]?.id;

  const params = useParams<{ id: string }>();

  const form = useRef<QuestionnaireFormTypeRef>(null);

  const { data: clientUploadData } = useGetInfoDetail<any, any>(idSecondStep ?? '');

  const { data: templates } = useGetListTemplates();

  const { data: dataSOW } = useGetInfoDetail<any, documentFileUpload>(idSOW ?? '');

  const { data: generatedQuestionnaire } = useGetInfoDetail<any, documentFileUpload>(currentStepId ?? '');

  const { updateQuestionAnswer } = useProjectUpdateQuestionAnswer();

  const { mutateAsync } = useUpdateStatusStep();

  const {
    completeStep,
    updateStatus,
  } = useWorkflowActions();

  useEffect(() => {
    if (clientUploadData?.stepInfo.length && clientUploadData?.stepInfo[0]?.infos?.length) {
      // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
      setCampaignSelected(clientUploadData?.stepInfo[0]?.infos[0]?.serviceOption);
    }
  }, [clientUploadData]);

  useEffect(() => {
    if (templates && campaignSelected) {
      const templateSelect = templates.filter(template => template.campaign === campaignSelected);
      let urlOptions: TemplateFiles[] = [];
      templateSelect.forEach(template => urlOptions = [...urlOptions, ...template.files]);
      // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
      setTemplateFile(urlOptions);
    }
  }, [templates, campaignSelected]);

  useEffect(() => {
    if (dataSOW && dataSOW.stepInfo.length) {
      const brief = dataSOW?.stepInfo[0]?.infos[0]?.value ?? '';
      // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
      setAnalysisBrief(brief);
    }
  }, [dataSOW]);

  const saveDataFromAI = React.useCallback(async (markdown: string, form: QuestionnaireResponse) => {
    const payload = {
      stepInfos: [
        markdown
          ? {
              order: 0,
              infos: [{
                value: markdown,
              }],
            }
          : null,
        form
          ? {
              order: 1,
              infos: [
                { form },
              ],
            }
          : null,
      ].filter((item): item is { order: number; infos: any[] } => item !== null),
    };

    await updateQuestionAnswer(
      payload,
      currentStepId ?? '',
    );

    mutateAsync({ id: currentStep?.id ?? '', status: EStatusTask.IN_PROGRESS });
  }, []);

  const getDataQuestionnaire = async (qualityData: any, quantityData: any) => {
    try {
      const [quantityResponse, qualityResponse] = await Promise.all([
        quantityData ? copilotkitAction(quantityData, EEndpointApiCopilotkit.QUANTITY) : null,
        qualityData ? copilotkitAction(qualityData, EEndpointApiCopilotkit.QUALITY) : null,
      ]);

      const quantity = quantityResponse?.data?.data?.data;

      const quality = qualityResponse?.data?.data ?? '';
      setIsLoading(false);
      setQuality(quality);
      if (quantity) {
        setQuantity(updateNameFieldForQuestionnaire(quantity));
      }

      if (!quality && !quantity) {
        return;
      }
      saveDataFromAI(quality, quantity);
    } catch (error) {
      console.log(error);
    }
  };

  const handleSelectType = (option: toggleOptionsType) => {
    setSelectedType(option.id);

    const valueForm = form.current?.onSubmitForm();
    if (valueForm && quantity) {
      const data: QuestionnaireResponse = {
        ...quantity,
        sections: [...valueForm],
      };
      setQuantity(data);
    }
  };

  const updateViewQuestionnaireOption = () => {
    const documentFiles = generatedQuestionnaire?.stepInfoPrevious[0]?.infos[0];
    const typeSelected = documentFiles?.typeSelected ?? [];

    const listFilter = toggleOptions.filter(o => typeSelected.includes(o.type));

    // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
    setOptionList(listFilter);

    // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
    setSelectedType(listFilter[0]?.id ?? 'quantitative');
  };

  useEffect(() => {
    if (generatedQuestionnaire && generatedQuestionnaire?.stepInfo.length) {
      const { stepInfo } = generatedQuestionnaire;
      const infosMarkdown = stepInfo.find(step => step.order === 0);
      const infosForm = stepInfo.find(step => step.order === 1);

      updateViewQuestionnaireOption();

      if (infosMarkdown) {
        const markdown = infosMarkdown.infos[0]?.value;

        // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
        setQuality(markdown);
      }
      if (infosForm) {
        const form = infosForm.infos[0]?.form;
        // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
        setQuantity(updateNameFieldForQuestionnaire(form));
      }

      if (currentStep?.status !== EStatusTask.COMPLETED) {
        // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
        setIsSaved(false);
      } else {
        // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
        setIsSaved(true);
      }

      // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
      setIsLoading(false);
    } else {
      if (generatedQuestionnaire?.stepInfoPrevious.length) {
        const documentFiles = generatedQuestionnaire?.stepInfoPrevious[0]?.infos[0];
        const files: any = documentFiles?.files ?? [];
        const typeSelected = documentFiles?.typeSelected ?? [];

        updateViewQuestionnaireOption();

        if (!templateFile.length) {
          return;
        }

        if (!analysisBrief) {
          return;
        }

        const basePayload = {
          project_id: params.id,
          additional_info_url: getFile(files),
          brief_analysis: analysisBrief,
        };
        const qualityData = {
          ...basePayload,
          ...templateFile.reduce((result, _template) => {
            // if (template.type === ETypeFile.BRIEF_TEMPLATE) {
            //   result.template_brief_url = [
            //     ...(result.template_brief_url || []),
            //     ...getFile([template.file]),
            //   ];
            // }
            // if (template.type === ETypeFile.BRIEF_QUESTION) {
            //   result.question_brief_url = [
            //     ...(result.question_brief_url || []),
            //     ...getFile([template.file]),
            //   ];
            // }
            result.questionnaire_template_url = [{
              key: 'https://minastik-store.s3.ap-northeast-1.amazonaws.com/templates/3.+Questionnaire_BrandBeatScore-Quali+.docx',
            }];
            return result;
          }, {} as any),
        };

        const quantityData = {
          ...basePayload,
          ...templateFile.reduce((result, _template) => {
            // if (template.type === ETypeFile.BRIEF_TEMPLATE) {
            //   result.template_brief_url = [
            //     ...(result.template_brief_url || []),
            //     ...getFile([template.file]),
            //   ];
            // }
            // if (template.type === ETypeFile.BRIEF_QUESTION) {
            //   result.question_brief_url = [
            //     ...(result.question_brief_url || []),
            //     ...getFile([template.file]),
            //   ];
            // }
            result.questionnaire_template_url = [{
              key: 'https://minastik-store.s3.ap-northeast-1.amazonaws.com/templates/Questionaire+Template.docx',
            }];
            return result;
          }, {} as any),
        };
        const isQuantity = typeSelected.includes(EOptionSelect.FORM);
        const isQuality = typeSelected.includes(EOptionSelect.MARKDOWN);

        getDataQuestionnaire(isQuality ? qualityData : null, isQuantity ? quantityData : null);
      }
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [generatedQuestionnaire, templateFile, analysisBrief]);

  const onApprove = async () => {
    if (!currentStepId || !currentTask) {
      return;
    }
    const valueForm = form.current?.onSubmitForm();

    const formData = {
      ...quantity,
      sections: valueForm ? [...valueForm] : quantity ? [...quantity.sections] : [],

    };
    const payload = {
      stepInfos: [
        quality
          ? {
              order: 0,
              infos: [{
                value: quality,
              }],
            }
          : null,
        (valueForm || quantity)
          ? {
              order: 1,
              infos: [
                { form: { ...formData } },
              ],
            }
          : null,
      ].filter((item): item is { order: number; infos: any[] } => item !== null),
    };

    await updateQuestionAnswer(
      payload,
      currentStepId ?? '',
    );

    // if (currentStep.status !== EStatusTask.COMPLETED || (isChanged && currentStep.status === EStatusTask.COMPLETED)) {
    //   // handleSendMessage();
    //   // updateQuestionAnswer({ stepInfos: [] }, nextStepId);
    // }
    if (currentStep.status !== EStatusTask.COMPLETED) {
      mutateAsync({ id: currentStep?.id ?? '', status: EStatusTask.COMPLETED });
      updateStatus(currentStep?.id ?? '', EStatusTask.COMPLETED);
    }
    if (currentTask && (currentTask.status !== EStatusTask.COMPLETED)) {
      mutateAsync({ id: currentTask?.id ?? '', status: EStatusTask.COMPLETED });
      updateStatus(currentTask?.id ?? '', EStatusTask.COMPLETED, true);
    }
    setIsSaved(true);
  };

  const onSaveAndNextStep = () => {
    completeStep(currentStepId ?? '');
  };

  useEffect(() => {
    if (!isCopied) {
      return;
    }
    // eslint-disable-next-line react-web-api/no-leaked-timeout
    setTimeout(() => {
      setIsCopied(false);
    }, 5000);
  }, [isCopied]);

  const onCopyLink = async () => {
    if (isCopied) {
      return;
    }
    const domain = window.location.origin;
    const link = `${domain}/questionnaire?id=${currentStep?.id ?? ''}`;
    await navigator.clipboard.writeText(link);
    setIsCopied(true);
  };

  return (
    isLoading
      ? (
          <div className="p-4 md:p-6 ">
            <div className="mb-1 md:mb-2">Analyzing</div>
            <ProjectCardSkeleton />
          </div>
        )
      : (
          <div className="relative p-4 md:p-6 ">
            {/* Toggle Button */}
            <div className="flex items-center justify-between sticky top-0 py-2 right-4 z-100 bg-white">
              <div className="flex p-1 bg-gray-100 rounded-lg w-fit">
                {optionList.map(option => (
                  <button
                    key={option.id}
                    type="button"
                    onClick={() => handleSelectType(option)}
                    className={`px-4 py-2 rounded-md text-sm font-medium transition-all duration-200 ${selectedType === option.id
                      ? 'bg-white text-black shadow-sm'
                      : 'text-gray-600 hover:text-gray-800'
                    }`}
                  >
                    {option.label}
                  </button>
                ))}
              </div>
              {(!isSaved && (
                <Button type="button" onClick={onApprove}>
                  <CheckBadgeIcon className="h-5 w-5 " />
                  Approve
                </Button>
              ))}

              { isSaved && (
                <Button type="button" onClick={onSaveAndNextStep}>
                  <SendIcon className="h-5 w-5 " />
                  Save & Next
                </Button>
              )}
            </div>

            {
              isSaved && (
                <div className="flex items-center justify-between p-4 border border-green-500 bg-green-50 rounded-md">
                  <div>
                    <h4 className="text-green-600 font-semibold text-lg">Ready to Copy!</h4>
                    <p className="text-green-500 text-sm">
                      The link to this Questionnaire is ready to be copied and sent to clients for answering.
                    </p>
                  </div>
                  <Button
                    onClick={onCopyLink}
                    className="flex items-center gap-2 bg-green-500 text-white px-4 py-2 rounded-md hover:bg-green-600"
                  >
                    {isCopied
                      ? (
                          <>
                            <CheckIcon className="w-5 h-5" />
                            Copied!
                          </>
                        )
                      : (
                          <>
                            <LinkIcon className="w-5 h-5" />
                            Copy link
                          </>
                        )}
                  </Button>
                </div>
              )
            }

            {/* Dynamic Component Based on Selection */}
            {selectedType === 'quantitative'
              ? (
                  <QuantityQuestionnaire ref={form} questionnaire={quantity} />
                )
              : (
                  <QualityView markdown={quality} />
                )}
          </div>
        )
  );
};

export default GeneratedQuestionnaire;
