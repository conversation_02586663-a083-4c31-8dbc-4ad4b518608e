'use client';

import Label from '@/shared/components/form/Label';
import FileUpload from '../initial-screening-form/FileUpload';
import React, { useEffect, useState } from 'react';
import type { IFileResponse, stateRouteAgent } from '@/shared/types/global';
import WorkflowNavigation from '../layout/WorkflowNavigation';
import { useClientFileUploaded, useCurrentStep, useCurrentTask, useWorkflowActions } from '@/features/project-management/stores/project-workflow-store';
import { useCoAgent } from '@copilotkit/react-core';
import { useGetInfoDetail } from '@/features/project-management/hooks';
import type { BriefAnalysisFlow } from '@/features/project-management/types/agent';
import type { documentFileUpload } from '@/features/project-management/types/project';
import { Env } from '@/core/config/Env';
import { AGENT_ROUTE_NAME } from '@/shared/constants/global';
import { useUpdateStatusStep } from '@/features/project-management/hooks/useUpdateStatusStep';
import Checkbox from '@/shared/components/form/input/Checkbox';
import { EOptionSelect } from '@/features/project-management/types/questionnaire';
import type { OptionSelectType } from '@/features/project-management/types/questionnaire';
import { compareObjectArray } from '@/shared/utils/compareObject';
import { useProjectUpdateQuestionAnswer } from '@/features/project-management/hooks/useProjectUpdateQuestionAnswer';
import { EStatusTask } from '@/features/project-management/types/workflow';

const DocumentUploadFile: React.FC = () => {
  const [_files, setFiles] = useState<IFileResponse[]>([]);

  const [isDisable, setIsDisable] = useState(false);

  const [initialFile, setInitialFile] = useState<IFileResponse[]>([]);

  const [optionSelected, setOptionSelected] = useState<string[]>([EOptionSelect.FORM]);
  // Custom Hook

  const currentStep = useCurrentStep();

  const currentTask = useCurrentTask();

  const currentStepId = currentStep?.id;

  const clientFileUploaded = useClientFileUploaded();

  const {
    // getNextStepId,
    setClientFileUploaded,
    completeStep,
  } = useWorkflowActions();

  const { updateQuestionAnswer } = useProjectUpdateQuestionAnswer();

  const { mutateAsync } = useUpdateStatusStep();

  const { data: fileUpload } = useGetInfoDetail<documentFileUpload, any>(currentStep?.id ?? '');

  //  Set Agent

  const { setState: _setCoAgentsState } = useCoAgent<stateRouteAgent<BriefAnalysisFlow>>({
    name: AGENT_ROUTE_NAME,
  });

  const OPTION_SELECTION: OptionSelectType[] = [{
    id: 1,
    value: EOptionSelect.FORM,
    label: 'Quantitative Research Questions',
    description: 'A set of multiple choice questions that aims to clarify quantity variables using numerical data and statistical analysis.',
    isSelected: false,
  }, {
    id: 2,
    value: EOptionSelect.MARKDOWN,
    label: 'Qualitative Questionnaire Research Questions',
    description: 'A set of open-ended questions that aims to gain a deeper understanding of the business.',
    isSelected: false,
  }];

  // const { appendMessage } = useCopilotChat();

  const saveInitialFile = (file: IFileResponse[]) => {
    const setInitialFileStore = () => {
      setInitialFile(file);
      setFiles(file);
    };

    setInitialFileStore();
  };

  const updateInitialFile = () => {
    const setFile = () => {
      setIsDisable(true);
      const files = ((fileUpload?.stepInfo[0]?.infos[0]?.files ?? []).map(
        (file: any) => ({
          mimeType: file.type,
          originalname: file.name,
          key: file.file,
          filename: file.name,
          url: `${Env.NEXT_PUBLIC_API_SERVER}/public/${file.file}`,
          _id: file.id,
        }),
      ));
      const typeSelected = fileUpload?.stepInfo[0]?.infos[0]?.typeSelected;

      setOptionSelected(typeSelected ?? [EOptionSelect.FORM]);

      saveInitialFile(files);
      setClientFileUploaded(files);
    };

    setFile();
  };

  useEffect(() => {
    saveInitialFile(clientFileUploaded);
    if (fileUpload && fileUpload.stepInfo.length) {
      updateInitialFile();
    }
  }, [fileUpload]);

  const handleCheckboxChange = (option: OptionSelectType, checked: boolean, element?: React.MouseEvent<HTMLDivElement, MouseEvent>) => {
    if (isDisable) {
      return;
    }

    if (element && (element.target as HTMLElement).nodeName === 'SPAN') {
      return;
    }
    if (checked) {
      setOptionSelected(prev => [...prev, option.value]);
    } else {
      if (optionSelected.length > 1) {
        setOptionSelected(prev => prev.filter((value: string) => value !== option.value));
      }
    }
  };

  const handleFilesChange = React.useCallback((uploadedFiles: IFileResponse[]) => {
    setFiles(uploadedFiles);
  }, []);

  const onSubmit = async () => {
    if (!currentStep || !currentStepId) {
      return;
    }
    const payload = {
      stepInfos: [{
        order: 0,
        infos: [{
          typeSelected: optionSelected,
          files: _files.map(file => ({
            ...file,
            file: file.key,
            name: file.originalname,
            type: file.mimeType,
            id: file._id,
          })),
        }],
      }],
    };

    const isChanged = !compareObjectArray(initialFile, _files);
    // const nextStepId = getNextStepId();

    await updateQuestionAnswer(
      payload,
      currentStepId,
    );
    if (currentStep.status !== EStatusTask.COMPLETED || (isChanged && currentStep.status === EStatusTask.COMPLETED)) {
      // handleSendMessage();
      // updateQuestionAnswer({ stepInfos: [] }, nextStepId);
    }
    if (currentStep.status !== EStatusTask.COMPLETED) {
      mutateAsync({ id: currentStep?.id ?? '', status: EStatusTask.COMPLETED });
    }
    if (currentTask && (currentTask.status !== EStatusTask.COMPLETED)) {
      mutateAsync({ id: currentTask?.id ?? '', status: EStatusTask.IN_PROGRESS });
    }

    completeStep(currentStepId);
  };

  return (
    <div className="relative">
      <div className="space-y-6 p-4 md:p-6">
        {/* Service Options Grid */}
        <div className="mb-6">
          <div className="flex flex-col gap-4 cursor-pointer">
            {OPTION_SELECTION.map((option, index) => (
              <div
                key={index}
                className="border rounded-lg p-4"
                onClick={(e) => {
                  handleCheckboxChange(option, !(optionSelected ?? []).includes(option.value), e);
                }}
              >
                <Checkbox
                  id={`id_${index}`}
                  label={option.label}
                  checked={(optionSelected ?? []).includes(option.value)}
                  onChange={() => {}}
                  disabled={isDisable}
                />
                <div className="mt-3 text-gray-400 pl-8">{option.description}</div>
              </div>
            ))}
          </div>
        </div>

        <Label htmlFor="files" className="mb-1.5 block text-primary">
          Attached Files
        </Label>
        <FileUpload isDisable={isDisable} initialFile={initialFile} onFilesChange={handleFilesChange} />
      </div>

      <WorkflowNavigation
        onComplete={onSubmit}
        nextButtonText="Generate"
        showPrevious={false}
      />
    </div>
  );
};

export default DocumentUploadFile;
