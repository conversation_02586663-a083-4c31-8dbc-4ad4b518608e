import { useDirty } from '@/features/project-management/contexts/DirtyStepContext';
import { GuardConfirmationModal } from '@/shared/components/modals/GuardConfirmationModal';
import { Button } from '@/shared/components/ui/button';
import Editor from '@/shared/components/ui/editor/editor';
import { MarkdownRenderer } from '@/shared/components/ui/markdown/MarkdownRenderer';
import { useRouteGuardWithDialog } from '@/shared/hooks/route-guard/use-route-guard-with-dialog';
import { ArrowUturnLeftIcon, CheckBadgeIcon, FileEditIcon } from '@/shared/icons';
import type { EditorContentChanged } from '@/shared/types/global';
import { useEffect, useState } from 'react';

type AnalysisDataViewType = {
  data: string;
  isHiddenApproveButton?: boolean;
  isHiddenBackButton?: boolean;
  isFinish?: boolean;
  stepId: string;
  onSubmit: (markdown: string) => void;
  onBack?: () => void;
  onConfirm?: (markdown: string) => void;
};
const QuestionnaireAnalysis: React.FC<AnalysisDataViewType> = ({
  data,
  isHiddenApproveButton,
  isHiddenBackButton = false,
  isFinish = false,
  stepId,
  onSubmit,
  onBack,
  onConfirm,
}) => {
  const [isEditMode, setIsEditMode] = useState<boolean>(false);

  const [form, setForm] = useState<string>('');

  const [markdown, setMarkdown] = useState<string>('');

  const [isSaved, _setIsSaved] = useState(true);

  const { registerStep, clearStep } = useDirty();

  const { showDialog, title, message, onConfirm: onConfirmGuard, onCancel } = useRouteGuardWithDialog({
    when: !isSaved,
    title: 'Unsaved Changes',
    message: 'You have unsaved changes in the form. Are you sure you want to leave?',
  });

  useEffect(() => {
    // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
    setMarkdown(data);
    // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
    setForm(data);
  }, [data]);

  const discardChange = () => {
    setForm(data);
    setIsEditMode(false);

    clearStep(stepId);
  };

  const toggleEditMode = () => {
    setIsEditMode(true);
  };

  const confirmChange = () => {
    setMarkdown(form);
    setIsEditMode(false);
    clearStep(stepId);
    if (onConfirm) {
      onConfirm(form);
    }
  };

  const compareMarkdown = (form: string) => {
    return form === data;
  };

  const handleChangeEditor = (data: EditorContentChanged) => {
    const { markdown } = data;
    setForm(markdown);

    const isChanged = compareMarkdown(markdown);

    _setIsSaved(isChanged);

    registerStep(stepId, () => !isChanged);
  };

  return (
    <div className="relative">
      <div>
        { isEditMode
          ? <Editor onChange={handleChangeEditor} value={markdown} />

          : <MarkdownRenderer content={markdown} />}
      </div>

      {markdown && (
        <div className="bg-white w-full p-2 flex items-center gap-4 justify-center sticky bottom-0">
          {isEditMode
            ? (
                <>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={discardChange}
                  >
                    Discard Change
                  </Button>

                  <Button
                    type="button"
                    onClick={confirmChange}
                  >
                    <CheckBadgeIcon className="h-5 w-5 " />
                    Confirm
                  </Button>
                </>
              )
            : (
                <>
                  {!isHiddenBackButton && (
                    <Button
                      type="button"
                      variant="outline"
                      onClick={onBack}
                    >
                      <ArrowUturnLeftIcon className="h-5 w-5 " />
                      Back
                    </Button>
                  )}

                  {!isFinish && (
                    <Button
                      type="button"
                      variant="outline"
                      onClick={toggleEditMode}
                    >
                      <FileEditIcon className="h-5 w-5 " />
                      Edit
                    </Button>
                  )}

                  {/* <Button
                      type="button"
                      variant="outline"
                    >
                      <ArrowDownTrayIcon className="h-5 w-5 " />
                    </Button> */}
                  {!isHiddenApproveButton && (
                    <Button
                      type="button"
                      onClick={() => onSubmit(markdown)}
                    >
                      <CheckBadgeIcon className="h-5 w-5 " />
                      Approve
                    </Button>
                  )}
                </>
              )}

        </div>
      )}

      {/* Modal for guard */}
      <GuardConfirmationModal
        open={showDialog}
        onOpenChange={() => {}}
        title={title}
        description={message}
        onConfirm={onConfirmGuard}
        onCancel={onCancel}
      />
    </div>
  );
};

export default QuestionnaireAnalysis;
