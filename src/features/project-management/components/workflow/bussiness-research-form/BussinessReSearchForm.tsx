'use client';

import { ArrowUpTrayIcon, XMarkIcon } from '@heroicons/react/24/outline';
import type { CreateResearchPayload } from './validation/research.validation';
import Input from '@/shared/components/form/input/InputField';
import Label from '@/shared/components/form/Label';
import Select from '@/shared/components/form/Select';
import { Button } from '@/shared/components/ui/button';
import { Modal } from '@/shared/components/ui/modal';
import { useModal } from '@/shared/hooks/useModal';
import { zodResolver } from '@hookform/resolvers/zod';
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { createResearchSchema } from './validation/research.validation';
import { cn } from '@/shared/utils/utils';
import { Plus } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { frameworkOptions, ResearchFramework } from '@/features/project-management/types/research';
import type { ResearchItem, ResearchTemplateCustomFile } from '@/features/project-management/types/research';
import { http } from '@/core/http/http';
import type { IFileResponse } from '@/shared/types/global';
import { toast } from 'sonner';
import { useProjectResearchFramework } from '@/features/project-management/hooks/useProjectResearchFramework';
import SearchableSelect from '@/shared/components/form/SearchableSelect';
import type { ItemFrameworkResponse } from '@/features/frameworks-templates/types';
import { EEndpointApiCopilotkit } from '@/shared/enums/global';
import { TEMPLATE_GENERATE_KEYS } from '@/features/project-management/constants/research';
import { LoadingPage } from '@/shared/components/Loading/LoadingPage';

export type ResearchFormData = {
  name: string;
  template: IFileResponse[];
  type: ResearchFramework;
  files: IFileResponse[];
  otherTemplate?: string;
  description?: string;
  frameWorkId: string;
  framework: ItemFrameworkResponse | null;
  templateId?: string;
  customFiles?: ResearchTemplateCustomFile[];
};

type ResearchFormProps = {
  onCreateResearch?: (data: ResearchFormData) => void;
  onUpdateResearch?: (research: ResearchFormData) => void;
  initialData?: ResearchItem;
  isEdit?: boolean;
  onClose?: () => void;
};

export function ResearchForm({
  onCreateResearch,
  onUpdateResearch,
  initialData,
  isEdit = false,
  onClose,
}: ResearchFormProps) {
  const { isOpen, openModal, closeModal } = useModal();
  const t = useTranslations('Project');
  const [uploadedFiles, setUploadedFiles] = useState<IFileResponse[]>([]);
  const [customTemplate, setCustomTemplate] = useState(
    isEdit && !initialData?.infos?.[0]?.frameWorkId ? initialData?.infos[0]?.otherTemplate : '',
  );

  const [customDescription, setCustomDescription] = useState('');
  const [templateSearch, setTemplateSearch] = useState('');

  // Fetch templates with search
  const { frameworkOptions: apiTemplateOptions, isLoading: isLoadingTemplates } = useProjectResearchFramework({
    searchQuery: templateSearch,
    limit: 50,
  });

  // Find initial template based on frameWorkId
  const initialTemplate = useMemo(() => {
    if (!isEdit || !initialData?.infos?.[0]?.framework || !initialData?.infos?.[0]?.templateId) {
      return null;
    }

    for (const tem of initialData?.infos?.[0]?.framework.templates) {
      if (tem.id === initialData?.infos?.[0]?.templateId) {
        return tem;
      }
    }

    return 'other';
  }, [isEdit, initialData?.infos, apiTemplateOptions]);

  const [isShowLoading, setIsShowLoading] = useState<boolean>(false);

  const abortControllerRef = useRef<AbortController | null>(null);

  // Default form values
  const defaultValues = useMemo(() => ({
    name: initialData?.name || '',
    framework: initialData?.infos?.[0]?.framework ?? null,
    type: initialData?.infos?.[0]?.researchType ?? ResearchFramework.DeskResearch,
    template: isEdit ? initialTemplate : null,
  }), [initialData, isEdit, initialTemplate]);

  // Initialize React Hook Form with Zod validation
  const {
    control,
    handleSubmit,
    reset,
    watch,
    setValue,
    formState: { errors, isSubmitting },
  } = useForm<CreateResearchPayload>({
    resolver: zodResolver(createResearchSchema),
    defaultValues,
  });

  // Watch template selection to show/hide file upload
  const selectedTemplate = watch('template');
  const selectedFramework = watch('framework');

  // Template options based on selected framework
  const templateOptions = useMemo(() => {
    if (selectedFramework === 'other') {
      return [{
        value: 'other',
        label: 'Other',
      }];
    }

    // If selectedFramework is a framework object from API
    if (selectedFramework && typeof selectedFramework === 'object') {
      const frameworkTemplates = selectedFramework.templates?.map((template: any) => ({
        value: template, // Template object with name and files
        label: template.name, // Display template name
      })) || [];

      return frameworkTemplates;
    }

    // If no framework selected, show only "Other"
    return [{
      value: 'other',
      label: 'Other',
    }];
  }, [selectedFramework]);

  // Reset template value when framework changes and auto-set to "other" when framework is "Other"
  useEffect(() => {
    if (selectedFramework === 'other') {
      setValue('template', 'other');
    } else if (selectedFramework && typeof selectedFramework === 'object' && selectedFramework.templates.every((t: any) => t?.id !== selectedTemplate?.id)) {
      setValue('template', null);
    }
  }, [selectedFramework, setValue]);

  const handleFileRemove = useCallback((fileToRemove: IFileResponse) => {
    setUploadedFiles(prev => prev.filter(file => file !== fileToRemove));
  }, []);

  const handleDragOver = useCallback((event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
  }, []);

  // Reset form and close modal
  const resetForm = useCallback(() => {
    reset(defaultValues);
    setUploadedFiles([]);
    setCustomTemplate(
      isEdit && !initialData?.infos?.[0]?.frameWorkId ? initialData?.infos[0]?.otherTemplate : '',
    );
    setUploadedFiles(
      isEdit && !initialData?.infos?.[0]?.frameWorkId ? (initialData?.infos[0]?.files ?? []) : [],
    );
    setCustomDescription('');
  }, [reset, defaultValues, isEdit, initialData]);

  // Close modal and reset form to default (empty) state
  const closeModalForm = useCallback(() => {
    closeModal();
    // Reset to empty form (not edit values)
    const emptyValues = {
      name: '',
      framework: null,
      type: ResearchFramework.DeskResearch,
      template: null,
    };
    reset(emptyValues);
    setUploadedFiles([]);
    setCustomTemplate('');
    // Clear edit state in parent component
    if (onClose) {
      onClose();
    }
  }, [closeModal, reset, onClose]);

  // Open modal automatically when in edit mode
  useEffect(() => {
    if (isEdit && initialData) {
      openModal();
    }
  }, [isEdit, initialData, openModal]);

  // Reset form when initialData changes (for edit mode)
  useEffect(() => {
    if (initialData) {
      reset(defaultValues);
      setCustomTemplate(
        isEdit && !initialData?.infos?.[0]?.frameWorkId ? initialData?.infos[0]?.otherTemplate : '',
      );
      setUploadedFiles(
        isEdit && !initialData?.infos?.[0]?.frameWorkId ? (initialData?.infos[0]?.files ?? []) : [],
      );
    }
  }, [initialData, defaultValues, reset, isEdit]);

  const onSubmit = async (data: CreateResearchPayload) => {
    try {
      const { template, name, framework, type } = data;
      const templateFiles: IFileResponse[] = [];
      const isOtherTemplate = template === 'other';

      if (!isOtherTemplate && template && typeof template === 'object') {
        // If template is a template object, get its files
        template.files?.forEach((f: any) => {
          templateFiles.push({ ...f.file, category: f.category });
        });
      }
      const customFiles: ResearchTemplateCustomFile[] = [];

      if (isOtherTemplate && !uploadedFiles.length) {
        setIsShowLoading(true);
        try {
          const baseUrl = window.location.origin;
          const abortController = new AbortController();
          abortControllerRef.current = abortController;

          const fetchPromises = TEMPLATE_GENERATE_KEYS.map((key) => {
            const data = {
              template_name: customTemplate,
              template_description: customDescription,
              example_template_url: [{ key }],
            };

            return fetch(`${baseUrl}/api/copilotkit-api`, {
              method: 'POST',
              body: JSON.stringify({ data, endpoint: EEndpointApiCopilotkit.TEMPLATE_GENERATE }),
              signal: abortControllerRef.current?.signal,
            });
          });

          const responses = await Promise.all(fetchPromises);
          const [questionnaireRes, reportTemplateRes] = await Promise.all(responses.map(res => res.json()));

          if (questionnaireRes?.data?.result) {
            customFiles.push({ ...questionnaireRes?.data?.result as ResearchTemplateCustomFile, category: 'questionaire' });
          }

          if (reportTemplateRes?.data?.result) {
            customFiles.push({ ...reportTemplateRes?.data?.result as ResearchTemplateCustomFile, category: 'report' });
          }
        } catch (error) {
          console.log(error);
          setIsShowLoading(false);
        } finally {
          setIsShowLoading(false);
        }
      }

      const formData: ResearchFormData = {
        name,
        framework,
        type,
        template: templateFiles,
        files: uploadedFiles,
        otherTemplate: isOtherTemplate ? customTemplate : (template && typeof template === 'object' ? template.name : ''),
        frameWorkId: isOtherTemplate ? '' : (framework && typeof framework === 'object' ? framework.id : ''),
        templateId: isOtherTemplate ? '' : (template && typeof template === 'object' ? template.id : ''),
        description: isOtherTemplate ? '' : customDescription,
        customFiles,
      };

      if (isEdit && initialData && onUpdateResearch) {
        onUpdateResearch(formData);
      } else if (onCreateResearch) {
        onCreateResearch(formData);
      }

      resetForm();
      closeModalForm();
    } catch (error) {
      console.error('Failed to save research:', error);
    }
  };

  const getFileResponse = async (file: File) => {
    const formData = new FormData();
    formData.append('file', file);

    try {
      const uploadResponse = http.post<IFileResponse>({
        url: '/files/upload',
        data: formData,
        options: {
          headers: { 'Content-Type': 'multipart/form-data' },
        },
      });

      const res = await uploadResponse;
      return res;
    } catch (error) {
      console.log(error);
      throw error;
    }
  };

  const handleUploadFile = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files || files.length === 0) {
      return;
    }

    try {
      toast.info('File is uploading');

      // Handle multiple files
      const uploadPromises = Array.from(files).map(async (file) => {
        const { data } = await getFileResponse(file);
        return data;
      });

      const uploadedFileResponses = await Promise.all(uploadPromises);
      const validFiles = uploadedFileResponses.filter(file => file !== null);

      if (validFiles.length > 0) {
        toast.success('File is uploaded success');
        setUploadedFiles(prev => [...prev, ...validFiles]);
      }
    } catch (error) {
      console.error('File upload error:', error);
      toast.error('Failed to upload files');
    }

    // Reset input value to allow selecting the same file again
    e.target.value = '';
  };

  const handleFileDrop = useCallback(async (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    const files = event.dataTransfer.files;

    if (!files || files.length === 0) {
      return;
    }

    try {
      toast.info('File is uploading');

      // Handle multiple files
      const uploadPromises = Array.from(files).map(async (file) => {
        const { data } = await getFileResponse(file);
        return data;
      });

      const uploadedFileResponses = await Promise.all(uploadPromises);
      const validFiles = uploadedFileResponses.filter(file => file !== null);

      if (validFiles.length > 0) {
        setUploadedFiles(prev => [...prev, ...validFiles]);
      }
    } catch (error) {
      console.error('File upload error:', error);
      toast.error('Failed to upload files');
    }
  }, []);

  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  return (
    <>
      {isShowLoading && <LoadingPage />}

      <Button
        className={cn('gap-2 h-[200px] w-full border-2 border-dashed border-muted-foreground/25 hover:border-primary/50')}
        variant="outline"
        onClick={openModal}
      >
        <Plus className="w-5 h-5" />
        {isEdit ? t('edit_research', { fallback: 'Edit Research' }) : t('create_research', { fallback: 'Create Research' })}
      </Button>

      <Modal isOpen={isOpen} onClose={closeModalForm} className="max-w-[580px] m-4">
        <div className="no-scrollbar relative w-full max-w-[580px] rounded-3xl bg-white p-6 dark:bg-gray-900 overflow-hidden">
          <div className="px-2 pr-14">
            <h4 className="mb-2 text-2xl font-medium text-gray-800 dark:text-white/90">
              {isEdit ? t('edit_research', { fallback: 'Edit Research' }) : t('create_research', { fallback: 'Create Research' })}
            </h4>
            <p className="mb-6 text-sm text-gray-500 dark:text-gray-400 lg:mb-7">
              {t('research_form_description', { fallback: 'Please enter all the required information to proceed.' })}
            </p>
          </div>

          <form onSubmit={handleSubmit(onSubmit)} className="flex flex-col">
            <div className="custom-scrollbar h-[350px] overflow-y-auto px-2 pb-3">
              <div className="grid grid-cols-1 gap-y-5">
                {/* Research Name */}
                <div>
                  <Label htmlFor="name">
                    {t('research_name', { fallback: 'Research name' })}
                    {' '}
                    <span className="text-error-500">*</span>
                  </Label>
                  <Controller
                    control={control}
                    name="name"
                    render={({ field }) => (
                      <Input
                        id="name"
                        {...field}
                        placeholder="Enter"
                        type="text"
                        error={!!errors.name}
                        hint={errors.name?.message}
                      />
                    )}
                  />
                </div>

                {/* Research type */}
                <div>
                  <Label htmlFor="type">
                    {t('research_type', { fallback: 'Research type' })}
                    {' '}
                    <span className="text-error-500">*</span>
                  </Label>
                  <Controller
                    control={control}
                    name="type"
                    render={({ field }) => (
                      <Select
                        options={frameworkOptions}
                        defaultValue={field.value}
                        onChange={value => field.onChange(+value)}
                        placeholder={t('select_framework', { fallback: 'Select framework' })}
                        className="w-full"
                      />
                    )}
                  />
                  {errors.type && (
                    <p className="text-sm text-red-500 mt-1">{errors.type.message}</p>
                  )}
                </div>

                {/* Research framework */}
                <div className="flex gap-3">
                  <div className="flex-1">
                    <Label htmlFor="framework">
                      {t('research_framework', { fallback: 'Research Framework' })}
                      {' '}
                      <span className="text-error-500">*</span>
                    </Label>
                    <Controller
                      control={control}
                      name="framework"
                      render={({ field }) => (
                        <SearchableSelect
                          options={apiTemplateOptions}
                          value={field.value || ''}
                          onChange={(value: string) => field.onChange(value)}
                          onSearchChange={setTemplateSearch}
                          searchValue={templateSearch}
                          placeholder={t('select_framework', { fallback: 'Select framework' })}
                          searchPlaceholder={t('search_frameworks', { fallback: 'Search frameworks...' })}
                          loading={isLoadingTemplates}
                          className="w-full"
                        />
                      )}
                    />
                    {errors.framework && (
                      <p className="text-sm text-red-500 mt-1">Research framework is required</p>
                    )}
                  </div>

                  {/* Research template */}
                  <div className="flex-1">
                    <Label htmlFor="template">
                      {t('research_template', { fallback: 'Research Template' })}
                      {' '}
                      <span className="text-error-500">*</span>
                    </Label>
                    <Controller
                      control={control}
                      name="template"
                      render={({ field }) => (
                        <SearchableSelect
                          options={templateOptions}
                          value={field.value || ''}
                          onChange={(value: string) => field.onChange(value)}
                          placeholder={t('select_template', { fallback: 'Select template' })}
                          className="w-full"
                        />
                      )}
                    />
                    {errors.template && (
                      <p className="text-sm text-red-500 mt-1">Research template is required</p>
                    )}

                  </div>
                </div>

                {/* Conditional fields when "Other" is selected */}
                {selectedTemplate === 'other' && (
                  <>
                    {/* Custom template description */}
                    <div>
                      <Label htmlFor="customTemplate" className="text-red-500">
                        Specify your selection or upload new template below
                      </Label>
                      <Input
                        id="customTemplate"
                        value={customTemplate}
                        onChange={e => setCustomTemplate(e.target.value)}
                        placeholder="Specify type of research"
                        type="text"
                      />
                    </div>

                    <div>
                      <Label htmlFor="customDescription" className="text-red-500">
                        Specify your description for new template below
                      </Label>
                      <Input
                        id="customDescription"
                        value={customDescription}
                        onChange={e => setCustomDescription(e.target.value)}
                        placeholder="Specify description of research"
                        type="text"
                      />
                    </div>

                    {/* File upload section */}
                    <div>
                      <Label htmlFor="fileUpload">
                        Template file upload
                      </Label>
                      {/* Drag and drop area */}
                      <div
                        className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center bg-gray-50 dark:bg-gray-800 dark:border-gray-600"
                        onDrop={e => handleFileDrop(e)}
                        onDragOver={handleDragOver}
                      >
                        <ArrowUpTrayIcon className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                        <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                          Drag & drop to upload
                        </p>
                        <p className="text-xs text-gray-500 dark:text-gray-500 mb-4">
                          Files supported: PPT, PDF, DOC, MP3, MP4, TXT
                        </p>
                        <p className="text-xs text-gray-500 dark:text-gray-500 mb-4">
                          or
                        </p>
                        <Button
                          type="button"
                          variant="outline"
                          onClick={() => document.getElementById('fileInput')?.click()}
                          className="inline-flex items-center gap-2"
                        >
                          <ArrowUpTrayIcon className="h-4 w-4" />
                          Browse files
                        </Button>
                        <input
                          id="fileInput"
                          type="file"
                          onChange={e => handleUploadFile(e)}
                          multiple
                          className="hidden"
                        />
                      </div>

                      {/* Uploaded files display */}
                      {uploadedFiles.length > 0 && (
                        <div className="mt-4 space-y-2">
                          <div className="text-sm font-medium text-gray-700 dark:text-gray-300">
                            Uploaded files (
                            {uploadedFiles.length}
                            )
                          </div>
                          {uploadedFiles.map((file, index) => (
                            <div
                              key={`${file.filename}-${index}`}
                              className="p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800"
                            >
                              <div className="flex items-center justify-between">
                                <div className="flex items-center gap-2">
                                  <div className="text-sm font-medium text-blue-900 dark:text-blue-100">
                                    {file.filename}
                                  </div>
                                </div>
                                <Button
                                  type="button"
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => handleFileRemove(file)}
                                  className="h-6 w-6 p-0 text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-200"
                                >
                                  <XMarkIcon className="h-4 w-4" />
                                </Button>
                              </div>
                            </div>
                          ))}

                          {/* Clear all files button */}
                          {uploadedFiles.length > 1 && (
                            <Button
                              type="button"
                              variant="outline"
                              size="sm"
                              onClick={() => setUploadedFiles([])}
                              className="text-red-600 hover:text-red-800 border-red-300 hover:border-red-400"
                            >
                              Clear all files
                            </Button>
                          )}
                        </div>
                      )}
                    </div>
                  </>
                )}
              </div>
            </div>

            <div className="flex items-center gap-3 px-2 mt-6 lg:justify-end">
              <Button variant="outline" onClick={closeModalForm} disabled={isSubmitting}>
                {t('cancel', { fallback: 'Cancel' })}
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting
                  ? (isEdit ? t('updating', { fallback: 'Updating...' }) : t('creating', { fallback: 'Creating...' }))
                  : (isEdit ? t('update', { fallback: 'Update' }) : t('create', { fallback: 'Create' }))}
              </Button>
            </div>
          </form>
        </div>
      </Modal>
    </>
  );
}
