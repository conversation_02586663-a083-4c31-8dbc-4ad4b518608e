import { useState } from 'react';
import { Calendar, User } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { Card, CardContent, CardHeader } from '@/shared/components/ui/card';
import { But<PERSON> } from '@/shared/components/ui/button';
import { Modal } from '@/shared/components/ui/modal';
import { Popover, PopoverContent, PopoverTrigger } from '@/shared/components/ui/popover';
import { Skeleton } from '@/shared/components/ui/skeleton';
import { CONVERT_RESEARCH_FRAMEWORK_TO_LABEL } from '@/features/project-management/types/research';
import type { ResearchItem } from '@/features/project-management/types/research';
import { MoreDotIcon, PencilIcon, TrashIcon } from '@/shared/icons';

// Skeleton component
export function ResearchCardSkeleton() {
  return (
    <Card className="h-[200px]">
      <CardHeader className="pb-3">
        <div className="flex justify-between items-start">
          <div className="flex-1">
            <Skeleton className="h-4 w-32 mb-2" />
            <Skeleton className="h-3 w-24" />
          </div>
          <Skeleton className="h-6 w-6 rounded" />
        </div>
      </CardHeader>
      <CardContent>
        <Skeleton className="h-3 w-full mb-2" />
        <Skeleton className="h-3 w-3/4 mb-4" />
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Skeleton className="h-6 w-6 rounded-full" />
            <Skeleton className="h-3 w-16" />
          </div>
          <Skeleton className="h-3 w-20" />
        </div>
      </CardContent>
    </Card>
  );
}

// Research card component
export function ResearchCard({ item, onEdit, onDelete, onSelectCard }: {
  item: ResearchItem;
  length?: number;
  onDelete?: (id: string) => void;
  onEdit?: (item: ResearchItem) => void;
  onSelectCard?: (item: ResearchItem) => void;
}) {
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);

  const t = useTranslations('Project');

  // Get the first info item (API response has only one element in infos array)
  const info = item.infos?.[0];

  const openDeleteConfirm = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setShowDeleteConfirm(true);
  };

  const closeDeleteConfirm = () => {
    setShowDeleteConfirm(false);
  };

  const handleDelete = async (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    closeDeleteConfirm();

    if (onDelete) {
      onDelete(item.id);
    }

    // console.log(length);
    // try {
    //   await deleteResearchItem(item.id);
    //   console.log(length);
    // } catch (err) {
    //   // Error is already handled in the hook with toast
    //   console.error('Delete failed:', err);
    // }
  };

  const handleSelectCard = () => {
    if (onSelectCard) {
      onSelectCard(item);
    }
  };

  const handleEdit = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (onEdit) {
      onEdit(item);
    }
  };

  return (
    <>
      <Card className="group hover:shadow-md transition-all duration-200 h-[200px] relative p-3 justify-between cursor-pointer" onClick={handleSelectCard}>
        <CardHeader className="pb-3 px-0">
          <div className="flex justify-between items-start">
            <div className="flex-1 min-w-0">
              {info && (
                <span className="text-xs text-blue-600 bg-blue-50 px-2 py-1 rounded-md">
                  {info.otherTemplate}
                </span>
              )}
            </div>
          </div>
        </CardHeader>

        <CardContent className="pt-0 px-0">
          <p className="font-semibold text-sm mb-1 ">{item.name}</p>

          {info && (
            <p className="text-sm text-muted-foreground mb-2 line-clamp-2">
              {CONVERT_RESEARCH_FRAMEWORK_TO_LABEL[info?.researchType ?? 0]}
            </p>
          )}

          <div className="flex items-center justify-between text-xs text-muted-foreground">
            <div className="flex items-center gap-2">
              <div className="w-6 h-6 rounded-full bg-primary/10 flex items-center justify-center">
                <User className="w-3 h-3" />
              </div>
            </div>
            <div className="flex items-center gap-1">
              <Calendar className="w-3 h-3" />
              <span>{new Date(item.createdAt).toLocaleDateString()}</span>
            </div>
          </div>

          {/* Action menu */}
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                onClick={e => e.stopPropagation()}
                className="absolute top-4 right-4 size-7 opacity-0 group-hover:opacity-100 transition-opacity z-10"
              >
                <MoreDotIcon className="size-4 text-foreground/70" />
                <span className="sr-only">Menu</span>
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-40 p-0" align="end">
              <div className="py-1">
                <Button
                  variant="ghost"
                  className="w-full justify-start rounded-none"
                  onClick={handleEdit}
                >
                  <PencilIcon className="size-4" />
                  {t('edit', { fallback: 'Edit' })}
                </Button>
                <Button
                  variant="ghost"
                  className="w-full justify-start text-destructive rounded-none"
                  onClick={openDeleteConfirm}
                >
                  <TrashIcon className="size-4" />
                  {t('delete', { fallback: 'Delete' })}
                </Button>
              </div>
            </PopoverContent>
          </Popover>
        </CardContent>
      </Card>

      {/* Delete Confirmation Modal */}
      <Modal isOpen={showDeleteConfirm} onClose={closeDeleteConfirm} className="max-w-md mx-auto">
        <div className="p-6">
          <h3 className="text-lg font-medium text-foreground mb-2">
            {t('delete_research', { fallback: 'Delete Research' })}
          </h3>
          <p className="text-sm text-muted-foreground my-6">
            {t('confirm_delete_research', {
              fallback: 'Are you sure you want to delete "{name}"? This action cannot be undone.',
              name: item.name,
            })}
          </p>
          <div className="flex justify-end space-x-3">
            <Button
              variant="outline"
              onClick={closeDeleteConfirm}
            >
              {t('cancel', { fallback: 'Cancel' })}
            </Button>

            <Button
              variant="destructive"
              onClick={handleDelete}
            >
              {t('delete')}
            </Button>
          </div>
        </div>
      </Modal>
    </>
  );
}
