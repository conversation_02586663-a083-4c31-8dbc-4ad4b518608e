'use client';

import { ChevronRightIcon, XIcon } from 'lucide-react';
import React from 'react';
import { Button } from '@/shared/components/ui/button';
import { useChatBoxToggle, useChatBoxVisible } from '@/features/project-management/stores/chatbox-store';

type TaskSidebarProps = {
  children?: React.ReactNode;
};

const TaskSidebar: React.FC<TaskSidebarProps> = ({ children }) => {
  const isVisible = useChatBoxVisible();
  const toggle = useChatBoxToggle();

  if (!isVisible) {
    return (
      <div className="absolute right-0 top-1/2 -translate-y-1/2">
        <Button
          onClick={toggle}
          variant="outline"
          className="h-12 w-6 rounded-l-md rounded-r-none border-r-0"
          aria-label="Open sidebar"
        >
          <ChevronRightIcon className="h-4 w-4" />
        </Button>
      </div>
    );
  }

  return (
    <div className="relative h-full animate-slide-left">
      <div className="p-6 bg-background border border-border h-full border-t-0 border-b-0 max-height-side-bar">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-medium text-foreground">
            AI Assistant
          </h3>
          <Button
            onClick={toggle}
            variant="secondary"
            size="sm"
            className="h-8 w-8 p-0"
            aria-label="Close sidebar"
          >
            <XIcon className="h-4 w-4" />
          </Button>
        </div>

        {children}
      </div>
    </div>
  );
};

export default TaskSidebar;
