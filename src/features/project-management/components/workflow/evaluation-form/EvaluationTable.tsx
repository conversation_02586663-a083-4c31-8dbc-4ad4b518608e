'use client';

import { useEvaluationActions, useSectionTotals } from '@/features/project-management/stores/project-workflow-store';
import type { IOptionsItem } from '../../../constants/mock-option';
import type { EvaluationCriteria, SectionType } from '../../../types/evaluation-form';

import Select from '@/shared/components/form/Select';
import {
  Table,
  TableBody,
  TableCell,
  TableFooter,
  TableHead,
  TableHeader,
  TableRow,
} from '@/shared/components/ui/table';
import { DocumentIcon } from '@/shared/icons';
import React, { useCallback, useMemo } from 'react';

type EvaluationTableProps = {
  sectionType: SectionType;
  disabled?: boolean;
  onChangeData: () => void;
};

// Pre-compute confidence colors to avoid recalculation
const getConfidenceColor = (confidence: string): string => {
  const value = Number.parseInt((confidence || '0')?.replace('%', ''));
  if (value >= 70) {
    return 'text-green-600 dark:text-green-400';
  }
  if (value >= 30) {
    return 'text-blue-500 dark:text-blue-400';
  }
  return 'text-red-600 dark:text-red-400';
};

// Memoized TableRow component to prevent unnecessary re-renders
const EvaluationTableRow = React.memo(({
  row,
  sectionType,
  disabled,
  getAnswerList,
  onChangeData,
  updateAnswer,
}: {
  row: EvaluationCriteria;
  sectionType: SectionType;
  getAnswerList: (type: string) => IOptionsItem[];
  onChangeData: () => void;
  updateAnswer: (sectionType: SectionType, id: string, answer: string, selectedIndex?: number) => void;
  disabled?: boolean;
}) => {
  // Memoize the answer list to prevent recalculation
  const answerList = useMemo(() => getAnswerList(row.type), [getAnswerList, row.type]);

  // Memoize the change handler to prevent recreation on each render
  const handleChange = useCallback((value: string) => {
    // Find the index of the selected option
    const index = answerList.findIndex(item => item.value === value);
    // Always update when a selection is made
    updateAnswer(sectionType, row.id, value, index);
    onChangeData();
  }, [answerList, row.id, sectionType, updateAnswer, onChangeData]);

  // Memoize the confidence color
  const confidenceColor = useMemo(() =>
    getConfidenceColor(row.confidence || ''), [row.confidence]);

  return (
    <TableRow key={`row-${row.id}-${row.answer}-${row.criteriaScore}-${row.convertedScore}`}>
      <TableCell>{row.criteria}</TableCell>
      <TableCell>
        <div className="relative">
          {/* <Select
            defaultValue={row.answer || ''}
            value={row.answer || ''}
            onValueChange={handleChange}
          >
            <SelectTrigger className="w-full" id="sort-select">
              <SelectValue placeholder="Sort by" />
            </SelectTrigger>
            <SelectContent>
              {answerList.map(option => (
                <SelectItem key={option.value} value={option.value}>{option.label}</SelectItem>
              ))}
            </SelectContent>
          </Select> */}

          <Select
            options={answerList}
            onChange={handleChange}
            defaultValue={row.answer || ''}
            isHiddenPlaceHolder={true}
            disabled={disabled}
          />
        </div>
      </TableCell>
      <TableCell className={`text-center ${confidenceColor}`}>
        {row.confidence}
        {' '}
        %
      </TableCell>
      <TableCell>
        {row.confidence !== '0' && (
          <div className="flex items-center gap-2 justify-center">
            <span className="cursor-pointer" title={row.citation}>
              <DocumentIcon className="h-5 w-5 text-gray-600 dark:text-gray-400" />
            </span>
          </div>
        )}
      </TableCell>
      <TableCell className="text-center">{row.criteriaType}</TableCell>
      <TableCell className="text-center">
        {row.weight}
        {' '}
        %
      </TableCell>
      <TableCell className="text-center">{row.criteriaScore}</TableCell>
      <TableCell className="text-center">{row.convertedScore}</TableCell>
    </TableRow>
  );
});

EvaluationTableRow.displayName = 'EvaluationTableRow';

// Main component - optimized to reduce re-renders
const EvaluationTable: React.FC<EvaluationTableProps> = ({ sectionType, disabled, onChangeData }: EvaluationTableProps) => {
  const sectionTotals = useSectionTotals();

  const { updateAnswer, getAnswerList, getSectionData } = useEvaluationActions();

  // Get section totals
  const sectionWeight = sectionTotals[sectionType]?.weight || '0.00';
  const sectionScore = sectionTotals[sectionType]?.score || '0.00';

  // Get section data directly from the store to ensure we always have the latest data
  const sectionData = getSectionData(sectionType);

  // Add a key to the table to force re-render when section data changes
  const tableKey = useMemo(() => {
    // Create a key based on the section data to force re-renders when data changes
    return `table-${sectionType}-${sectionData.map(row => `${row.id}-${row.answer}-${row.criteriaScore}`).join('-')}`;
  }, [sectionType, sectionData]);

  return (
    <div className="w-full overflow-x-auto">
      <Table key={tableKey}>
        <TableHeader>
          <TableRow>
            <TableHead className="w-1/6">Evaluation Criteria</TableHead>
            <TableHead className="w-1/6">Answer</TableHead>
            <TableHead className="w-1/12 text-center">Confidence</TableHead>
            <TableHead className="w-1/12 text-center">Citation</TableHead>
            <TableHead className="w-1/8 text-center">Criteria Type</TableHead>
            <TableHead className="w-1/12 text-center">Weight</TableHead>
            <TableHead className="w-1/12 text-center">Criteria Score</TableHead>
            <TableHead className="w-1/12 text-center">Converted Score</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {sectionData.map(row => (
            <EvaluationTableRow
              key={`${row.id}-${row.answer}-${row.criteriaScore}-${row.convertedScore}`}
              row={row}
              sectionType={sectionType}
              getAnswerList={getAnswerList}
              updateAnswer={updateAnswer}
              disabled={disabled}
              onChangeData={onChangeData}
            />
          ))}
        </TableBody>
        <TableFooter>
          <TableRow className="bg-muted">
            <TableCell colSpan={5} className="font-medium ">Total Scoring</TableCell>
            <TableCell className="font-medium text-center">
              {sectionWeight}
              {' '}
              %
            </TableCell>
            <TableCell></TableCell>
            <TableCell className="font-medium text-center">{sectionScore}</TableCell>
          </TableRow>
        </TableFooter>
      </Table>
    </div>
  );
};

export default EvaluationTable;
