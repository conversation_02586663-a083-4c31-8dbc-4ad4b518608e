import { CopilotChat } from '@copilotkit/react-ui';
import React from 'react';
import { useChatBoxVisible } from '@/features/project-management/stores/chatbox-store';
import { AssistantAvatar, UserAvatar } from './ChatAvatar';

/**
 * ChatBox component that displays a chat interface
 *
 * The visibility is controlled by the useChatBoxVisibility hook
 */
export function ChatBox() {
  // Get the visibility state from the custom hook
  const isVisible = useChatBoxVisible();

  // If not visible, don't render the component
  if (!isVisible) {
    return null;
  }

  return (
    <CopilotChat
      className=""
      instructions="You are assisting the user as best as you can. Answer in the best way possible given the data you have."
      labels={{
        title: 'Your Assistant',
        initial: 'Please wait a moment!',
      }}
      UserMessage={UserAvatar}
      AssistantMessage={AssistantAvatar}
    />
  );
}
