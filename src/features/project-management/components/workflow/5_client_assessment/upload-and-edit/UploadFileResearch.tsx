import { But<PERSON> } from '@/shared/components/ui/button';
import { BoxCubeIcon } from '@/shared/icons';
import FileUpload from '../../initial-screening-form/FileUpload';
import React, { useMemo, useState } from 'react';
import type { IFileResponse } from '@/shared/types/global';
import { toast } from 'sonner';
import { useProjectUpdateQuestionAnswer } from '@/features/project-management/hooks/useProjectUpdateQuestionAnswer';
import { useDirty } from '@/features/project-management/contexts/DirtyStepContext';
import { compareObjectArray } from '@/shared/utils/compareObject';
import { GuardConfirmationModal } from '@/shared/components/modals/GuardConfirmationModal';
import { useRouteGuardWithDialog } from '@/shared/hooks/route-guard/use-route-guard-with-dialog';
import { useUpdateStatusStep } from '@/features/project-management/hooks/useUpdateStatusStep';
import { EDeskResearch } from '@/features/project-management/types/questionnaire';
import { EStatusTask } from '@/features/project-management/types/workflow';

type UploadFileResearchType = {
  showViewButton?: boolean;
  data: any[];
  initialData: any[];
  id: string;
  type: number;
  stepId: string;
  changeNextView: ((type: 'upload' | 'editor') => void);
  onBackDashboard: () => void;
  onViewData: () => void;
};

const UploadFileResearch: React.FC<UploadFileResearchType> = ({
  showViewButton,
  data,
  id,
  type,
  stepId,
  initialData,
  changeNextView,
  onBackDashboard,
  onViewData,
}) => {
  const [initialFile, _setInitialFile] = useState<IFileResponse[]>(() => data.length ? data[0].files : []);

  const [files, setFiles] = useState<IFileResponse[]>(() => data.length ? data[0].files : []);

  const [isSaved, _setIsSaved] = useState(true);

  const [isShowModal, setIsShowModal] = useState(false);

  const titleConfirm = 'Confirm Changes';

  const descriptionConfirm = 'Are you sure you want to make this change? Changing the status will result in the deletion of all related information for this step';

  const { registerStep, clearStep } = useDirty();

  const { showDialog, title, message, onConfirm, onCancel } = useRouteGuardWithDialog({
    when: !isSaved,
    title: 'Unsaved Changes',
    message: 'You have unsaved changes in the form. Are you sure you want to leave?',
  });

  const { updateQuestionAnswer } = useProjectUpdateQuestionAnswer();

  const { mutateAsync: updateStatusStepData } = useUpdateStatusStep();

  const handleFilesChange = React.useCallback((uploadedFiles: IFileResponse[]) => {
    setFiles(uploadedFiles);

    const isChanged = !compareObjectArray(initialFile, uploadedFiles);

    _setIsSaved(!isChanged);

    registerStep(stepId, () => isChanged);
  }, []);

  const isChanged = useMemo(() => {
    return !compareObjectArray(initialFile, files);
  }, [initialFile, files]);

  const saveStepInfos = async () => {
    clearStep(stepId);
    const payload = {
      formStepId: id,
      stepInfos: [
        {
          order: 0,
          type,
          infos: [{
            files: files.map(file => ({
              ...file,
              file: file.key,
              name: file.originalname,
              type: file.mimeType,
              id: file._id,
            })),
          }],
        },
      ],
    };

    await updateQuestionAnswer(payload, stepId);

    setTimeout(() => {
      changeNextView('editor');
    });
  };

  const updateStatusStep = async () => {
    const ids = initialData.filter(data => data.type !== EDeskResearch.UPLOAD_FILE).map(t => t.id);

    await updateStatusStepData({
      id: stepId,
      status: EStatusTask.COMPLETED,
      stepIds: [],
      stepInfoIds: ids,
      select: 'all',
      isGenerate: true,
    });
  };

  const resetFile = () => {
    setFiles(initialFile);
    onViewData();
  };

  const handleConfirmPopUp = async () => {
    await updateStatusStep();
    await saveStepInfos();
  };

  const handleCancelPopUp = () => {
    setIsShowModal(false);
  };

  const handleGenerateData = async () => {
    if (!files.length) {
      toast.error('You need to upload a file before proceeding.');
      return;
    }

    const isChanged = !compareObjectArray(initialFile, files);

    if (isChanged && initialFile.length) {
      setIsShowModal(true);
      return;
    }

    await saveStepInfos();
  };

  return (
    <>
      <div className="flex items-center justify-between gap-3">
        <div className="flex flex-col gap-2">
          <h2 className="text-xl font-medium text-foreground mb-0">
            Document Upload & Research Analysis
          </h2>

          <p className="mb-0">Upload related documents below to start generate the analysis</p>

        </div>

        <Button
          variant="secondary"
          onClick={onBackDashboard}
        >
          <BoxCubeIcon className="h-5 w-5" />
          Back to dashboard
        </Button>
      </div>

      <div className="my-2">
        Upload files
      </div>
      <FileUpload onFilesChange={handleFilesChange} initialFile={initialFile} background="bg-gray-100" />

      <div className={`sticky bottom-0 left-0 right-0  border-border p-4 z-20 flex justify-center gap-4 `}>

        {showViewButton && (
          <Button
            type="button"
            variant="outline"
            onClick={resetFile}
          >
            View Research
          </Button>
        )}

        <Button
          disabled={!isChanged}
          type="button"
          onClick={handleGenerateData}
        >
          Generate
        </Button>
      </div>

      {/* Modal for confirm content */}
      <GuardConfirmationModal
        open={isShowModal}
        onOpenChange={() => {}}
        title={titleConfirm}
        description={descriptionConfirm}
        onConfirm={() => handleConfirmPopUp()}
        onCancel={() => handleCancelPopUp()}
        confirmText="Continue"
        cancelText="Cancel"
      />

      {/* Modal for guard */}
      <GuardConfirmationModal
        open={showDialog}
        onOpenChange={() => {}}
        title={title}
        description={message}
        onConfirm={onConfirm}
        onCancel={onCancel}
      />
    </>

  );
};

export default UploadFileResearch;
