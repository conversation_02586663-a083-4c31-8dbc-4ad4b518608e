import { useDirty } from '@/features/project-management/contexts/DirtyStepContext';
import { GuardConfirmationModal } from '@/shared/components/modals/GuardConfirmationModal';
import { Button } from '@/shared/components/ui/button';
import Editor from '@/shared/components/ui/editor/editor';
import { htmlToMarkdownVer2, markdownToHtmlVer2 } from '@/shared/components/ui/editor/parser';
import { useRouteGuardWithDialog } from '@/shared/hooks/route-guard/use-route-guard-with-dialog';
import type { EditorContentChanged } from '@/shared/types/global';
import { useEffect, useState } from 'react';

type TextUploadEditorType = {
  markdown: string;
  stepId: string;
  data: any[];
  typeView: string;
  markdownSummarized: string;
  onChangeEditMode: (status: boolean) => void;
  onDataChange: (markdown: string) => void;
  setMarkdownSummarized: (data: string) => void;
};

const TextUploadEditor: React.FC<TextUploadEditorType> = ({
  markdown,
  stepId,
  typeView,
  markdownSummarized,
  onChangeEditMode,
  onDataChange,
}) => {
  const [valueText, setValueText] = useState<string>('');

  const [form, setForm] = useState<string>('');

  const [isSaved, _setIsSaved] = useState(true);

  const { registerStep, clearStep } = useDirty();

  const [isShowModal, setIsShowModal] = useState(false);

  const titleConfirm = 'Confirm Changes';

  const descriptionConfirm = 'Are you sure you want to make this change? Changing the status will result in the deletion of all related information for this step';

  const { showDialog, title, message, onConfirm, onCancel } = useRouteGuardWithDialog({
    when: !isSaved,
    title: 'Unsaved Changes',
    message: 'You have unsaved changes in the form. Are you sure you want to leave?',
  });

  useEffect(() => {
    if (markdown || markdownSummarized) {
      const initializeValueText = async () => {
        const html = await markdownToHtmlVer2(typeView === 'research' ? markdown : markdownSummarized);
        const markdownConvert = (htmlToMarkdownVer2(html));
        setValueText(markdownConvert);
        setForm(markdownConvert);
      };
      initializeValueText();
    }
  }, [markdown, markdownSummarized, typeView]);

  const compareMarkdown = (form?: string) => {
    return (typeView === 'research' ? markdown : markdownSummarized) === (form);
  };

  const onEditorChange = (data: EditorContentChanged) => {
    const { markdown } = data;
    setForm(markdown);

    const isChanged = compareMarkdown(markdown);

    _setIsSaved(isChanged);

    registerStep(stepId, () => !isChanged);
  };

  const handleDiscardChange = () => {
    setForm(valueText);
    clearStep(stepId);
    onChangeEditMode(false);
  };

  const handleConfirmChange = async () => {
    onDataChange(form);
    setValueText(form);
    clearStep(stepId);
    onChangeEditMode(false);
  };

  const handleCancelPopUp = () => {
    setIsShowModal(false);
  };

  const handleConfirmPopUp = async () => {
    onDataChange(form);
    setValueText(form);
    clearStep(stepId);
    onChangeEditMode(false);
    setIsShowModal(false);
  };

  return (
    <>
      <div className="sticky top-[70px] bg-white z-4">
        <div className="flex items-center justify-between gap-3">
          <div className="flex flex-col gap-2">
            <h2 className="text-xl font-medium mb-0">
              Edit Answer
            </h2>

            <p className="mb-0">Human-in-the-loop: edit any AI-generated content to finalize answers.</p>

          </div>

          <div className="flex items-center gap-2">
            <Button
              onClick={handleDiscardChange}
              variant="secondary"
            >
              Discard changes
            </Button>
            <Button
              onClick={handleConfirmChange}
              variant="default"
            >
              Confirm
            </Button>
          </div>
        </div>

      </div>
      <div className="mt-2 relative">
        <Editor
          classCustom="jodit-top-header"
          onChange={e => onEditorChange(e)}
          value={
            typeView === 'research'
              ? markdown
              : markdownSummarized
          }
        />

      </div>

      {/* Modal for confirm content */}
      <GuardConfirmationModal
        open={isShowModal}
        onOpenChange={() => {}}
        title={titleConfirm}
        description={descriptionConfirm}
        onConfirm={() => handleConfirmPopUp()}
        onCancel={() => handleCancelPopUp()}
        confirmText="Continue"
        cancelText="Cancel"
      />

      {/* Modal for guard */}
      <GuardConfirmationModal
        open={showDialog}
        onOpenChange={() => {}}
        title={title}
        description={message}
        onConfirm={onConfirm}
        onCancel={onCancel}
      />
    </>
  );
};

export default TextUploadEditor;
