import { useEffect, useState } from 'react';
import TextUploadEditor from './TextUploadEditor';
import TextUploadMarkdown from './TextUploadMarkdown';
import type { IFileResponse } from '@/shared/types/global';

type TextUpLoadType = {
  data: any[];
  templates: IFileResponse[];
  stepId: string;
  id: string;
  evaluationFramework: string;
  nameForm: string;
  onBackDashboard: () => void;
  onBackUploadFile: () => void;
  getMarkdown: (markdown: string) => void;
  onOpenDetailScore: (data: string) => void;
};

type ResearchTypeView = 'research' | 'summarized';

const TextUploadWrapper: React.FC<TextUpLoadType> = ({
  data,
  templates,
  id,
  stepId,
  evaluationFramework,
  nameForm,
  onBackDashboard,
  onBackUploadFile,
  getMarkdown,
  onOpenDetailScore,
}) => {
  const [isEditMode, setIsEditMode] = useState<boolean>(false);

  const [markdown, setMarkdown] = useState<string>('');

  const [markdownSummarized, setMarkdownSummarized] = useState<string>('');

  const [typeView, setTypeView] = useState<ResearchTypeView>('research');

  useEffect(() => {
    getMarkdown(markdown);
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [markdown]);

  const handleChange = (data: string) => {
    if (typeView === 'research') {
      setMarkdown(data);
    } else {
      setMarkdownSummarized(data);
    }
  };

  return (
    isEditMode
      ? (
          <TextUploadEditor
            markdown={markdown}
            stepId={stepId}
            data={data}
            markdownSummarized={markdownSummarized}
            typeView={typeView}
            setMarkdownSummarized={setMarkdownSummarized}
            onChangeEditMode={setIsEditMode}
            onDataChange={handleChange}
          />
        )
      : (
          <TextUploadMarkdown
            markdown={markdown}
            markdownSummarized={markdownSummarized}
            data={data}
            templates={templates}
            id={id}
            stepId={stepId}
            nameForm={nameForm}
            typeView={typeView}
            evaluationFramework={evaluationFramework}
            setMarkdown={setMarkdown}
            onBackDashboard={onBackDashboard}
            onBackUploadFile={onBackUploadFile}
            setIsEditResearch={setIsEditMode}
            onOpenDetailScore={onOpenDetailScore}
            setTypeView={setTypeView}
            setMarkdownSummarized={setMarkdownSummarized}
          />
        )
  );
};

export default TextUploadWrapper;
