import { <PERSON>down<PERSON>ender<PERSON> } from '@/shared/components/ui/markdown/MarkdownRenderer';
import React, { useEffect, useRef, useState } from 'react';
import HeaderResearch from '../HeaderResearch';
import { EDeskResearch } from '@/features/project-management/types/questionnaire';
import type { OptionChangeViewType } from '@/features/project-management/types/questionnaire';
import { toast } from 'sonner';
import { EEndpointApiCopilotkit } from '@/shared/enums/global';
import { useParams } from 'next/navigation';
import type { IFileResponse } from '@/shared/types/global';
import { getFile } from '@/features/project-management/utils/initialScreeningUtils';
import { useProjectUpdateQuestionAnswer } from '@/features/project-management/hooks/useProjectUpdateQuestionAnswer';
import { EStatusTask } from '@/features/project-management/types/workflow';
import { useCurrentStep, useProjectName, useWorkflowActions } from '@/features/project-management/stores/project-workflow-store';
import type { ScoringReportDataType } from '@/features/project-management/types/project';
import ProjectCardSkeleton from '../../../project-list/ProjectCardSkeleton';
import { useUpdateStatusStep } from '@/features/project-management/hooks/useUpdateStatusStep';
import { useQueryClient } from '@tanstack/react-query';
import { markdownToHTMLToDocFile } from '@/shared/components/ui/editor/parser';
import { downloadMDToFile } from '@/shared/utils/convertMDtoDocFile';

type ResearchTypeView = 'research' | 'summarized';

type TextUploadMarkdownType = {
  data: any[];
  templates: IFileResponse[];
  markdown: string;
  id: string;
  stepId: string;
  evaluationFramework: string;
  markdownSummarized: string;
  typeView: ResearchTypeView;
  nameForm: string;
  setMarkdown: (markdown: string) => void;
  onBackDashboard: () => void;
  onBackUploadFile: () => void;
  setIsEditResearch: (status: boolean) => void;
  onOpenDetailScore: (data: string) => void;
  setTypeView: (type: ResearchTypeView) => void;
  setMarkdownSummarized: (data: string) => void;
};

const TextUploadMarkdown: React.FC<TextUploadMarkdownType> = ({
  data,
  templates,
  markdown,
  id,
  stepId,
  evaluationFramework,
  typeView,
  nameForm,
  markdownSummarized: summary,
  setMarkdown,
  onBackDashboard,
  onBackUploadFile,
  setIsEditResearch,
  onOpenDetailScore,
  setTypeView,
  setMarkdownSummarized: setMarkdownSum,
}) => {
  const toggleOptions = [
    { id: 'research' as ResearchTypeView, label: 'Research Data' },
  ];
  const summarizeOption
  = { id: 'summarized' as ResearchTypeView, label: 'Summarized Report' };

  const [optionList, setOptionList] = useState<OptionChangeViewType[]>(toggleOptions);

  const [selectedType, setSelectedType] = useState<ResearchTypeView>(typeView);

  const [isLoading, setIsLoading] = useState<boolean>(() => !markdown);

  const [isLoadingSummarized, setIsLoadingSummarized] = useState<boolean>(true);

  const [_isViewCopyBox, setIsViewCopyBox] = useState<boolean>(false);

  const [isViewEditButton, _setIsViewEditButton] = useState<boolean>(true);

  const [isGen, setIsGen] = useState<boolean>(false);

  const [scoringReportData, setScoringReportData] = useState<ScoringReportDataType | null>(null);

  const params = useParams<{ id: string }>();

  const { updateQuestionAnswer } = useProjectUpdateQuestionAnswer();

  const { moveToNextStep } = useWorkflowActions();

  const { mutateAsync: updateDataStatus } = useUpdateStatusStep();

  const queryClient = useQueryClient();

  const currentStep = useCurrentStep();

  const projectName = useProjectName();

  const abortControllerRef = useRef<AbortController | null>(null);

  const saveDataInStep = async (markdown: string, type: EDeskResearch, order: number, status: EStatusTask) => {
    const payload = {
      formStepId: id,
      stepInfos: [
        {
          type,
          order,
          infos: [{ value: markdown, status, isFinish: EDeskResearch.SUMMARIZED === type }],
        },
      ],
    };

    setMarkdownSum('');
    await updateQuestionAnswer(payload, stepId);
  };

  const getDataFromAI = async (additionalFile: IFileResponse[]) => {
    const payload = {
      project_id: params.id,
      template_research_url: [...getFile(selectedType === 'research' ? templates.filter(t => t.category === 'questionnaire') : templates.filter(t => t.category === 'report'))],
      additional_info_url: [...getFile(additionalFile)],
    };

    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    try {
      const baseUrl = window.location.origin;
      const abortController = new AbortController();
      abortControllerRef.current = abortController;

      const response = await fetch(`${baseUrl}/api/copilotkit-api`, {
        method: 'POST',
        body: JSON.stringify({ data: payload, endpoint: EEndpointApiCopilotkit.DESK_RESEARCH_ANSWER_QUESTION }),
        signal: abortControllerRef.current.signal,
      });

      const res = await response.json();

      const markdown = res.data.result;
      setMarkdown(markdown);
      saveDataInStep(markdown, EDeskResearch.RESEARCH, 1, EStatusTask.IN_PROGRESS);
      setIsLoading(false);
    } catch (error: any) {
      console.error(error);
    }
  };

  const getScoringContent = async (data: string) => {
    const payload = {
      project_id: params.id,
      evaluation_framework: evaluationFramework,
      content_to_score: data,
    };
    try {
      const baseUrl = window.location.origin;
      const abortController = new AbortController();
      abortControllerRef.current = abortController;
      const response = await fetch(`${baseUrl}/api/copilotkit-api`, {
        method: 'POST',
        body: JSON.stringify({ data: payload, endpoint: EEndpointApiCopilotkit.SCORING_CONTENT }),
        signal: abortControllerRef.current.signal,
      });

      const res = await response.json();
      const evaluationReport = res.data.result.evaluate_report;
      const evaluationScore = res.data.result.score;

      const data = { report: evaluationReport, score: evaluationScore };
      const payloadScoring = {
        formStepId: id,
        stepInfos: [
          {
            type: EDeskResearch.SCORING,
            order: 3,
            infos: [{ value: data }],
          },
        ],
      };

      await updateQuestionAnswer(payloadScoring, stepId);

      setScoringReportData(data);
    } catch (error: any) {
      console.log(error);
    }
  };

  const getDataSummarized = async () => {
    const payload = {
      project_id: params.id,
      report_template_url: [...getFile(selectedType === 'research' ? templates.filter(t => t.category === 'questionnaire') : templates.filter(t => t.category === 'report'))],
      desk_research_answer: markdown,
    };
    if (abortControllerRef.current) {
      return;
    }
    try {
      const baseUrl = window.location.origin;
      const abortController = new AbortController();
      abortControllerRef.current = abortController;

      const response = await fetch(`${baseUrl}/api/copilotkit-api`, {
        method: 'POST',
        body: JSON.stringify({ data: payload, endpoint: EEndpointApiCopilotkit.DESK_RESEARCH_REPORT }),
        signal: abortControllerRef.current.signal,
      });

      const res = await response.json();
      const markdown = res.data.result;
      await getScoringContent(markdown);
      saveDataInStep(markdown, EDeskResearch.SUMMARIZED, 2, EStatusTask.COMPLETED);
      setIsViewCopyBox(false);
    } catch (error: any) {
      console.log(error);
    }
  };

  useEffect(() => {
    if (data.length) {
      const dataTextUpload = data.filter(d => d.type !== EDeskResearch.UPLOAD_FILE);
      if (!dataTextUpload.length) {
        // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
        setIsLoading(true);
        const additionalFile = data.find (d => d.type === EDeskResearch.UPLOAD_FILE);
        if (additionalFile) {
          const files = additionalFile.infos[0].files;
          getDataFromAI(files);
        }
      } else {
        const researchData = dataTextUpload.find(d => d.type === EDeskResearch.RESEARCH);
        const summarizedData = dataTextUpload.find(d => d.type === EDeskResearch.SUMMARIZED);
        const scoringData = dataTextUpload.find(d => d.type === EDeskResearch.SCORING);
        if (researchData) {
          const markdownRes = researchData.infos[0].value;
          const status = researchData.infos[0].status;

          if (!markdown) {
            setMarkdown(markdownRes);
          }

          if (selectedType === 'summarized' && !markdownRes) {
            abortControllerRef.current = null;
            getDataSummarized();
          }
          // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
          setIsViewCopyBox(status === EStatusTask.COMPLETED);
          if (status === EStatusTask.COMPLETED) {
            // setIsViewEditButton(false);

            if (optionList.length === 1) {
            // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
              setOptionList([...toggleOptions, summarizeOption]);
            }
          }
          // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
          setIsLoading(false);
        }

        if (summarizedData) {
          const markdown = summarizedData.infos[0].value;
          if (!summary) {
            setMarkdownSum(markdown);
          }

          // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
          setIsLoadingSummarized(false);
        }

        if (scoringData) {
          const data = scoringData.infos[0].value;
          // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
          setScoringReportData(data);
        }

        const isGenInform = data.some(d => d.isGenerate && d.order === 2);
        // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
        setIsGen(isGenInform);
      }
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [data]);

  useEffect(() => {
    setTypeView(selectedType);
  }, [selectedType, setTypeView]);

  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  const handleSelectType = async (option: OptionChangeViewType) => {
    if (option.id === 'summarized') {
      // setIsViewEditButton(false);
      if (!summary) {
        setIsLoadingSummarized(true);
        abortControllerRef.current = null;
        getDataSummarized();
      }
    }

    setSelectedType(option.id as ResearchTypeView);
  };

  const onSubmit = async () => {
    const id: string[] = data.filter(d => d.order !== 0 && d.order !== 1).map(t => t.id);
    await updateDataStatus({
      id: stepId,
      status: EStatusTask.COMPLETED,
      stepIds: [],
      stepInfoIds: [],
      select: 'all',
      isGenerate: true,
      stepInfoIdsGenerate: (selectedType === 'research') ? id : [],
    });
    if (selectedType === 'research') {
      setIsLoading(true);
      await saveDataInStep(markdown, EDeskResearch.RESEARCH, 1, EStatusTask.COMPLETED);

      await queryClient.invalidateQueries({ queryKey: ['getInfoDetail', stepId], type: 'all' });

      toast.success('Save research data successfully');
    } else {
      setIsLoadingSummarized(true);
      setMarkdownSum('');
      await saveDataInStep(summary, EDeskResearch.SUMMARIZED, 2, EStatusTask.COMPLETED);
    }
    // setIsViewEditButton(false);
  };

  const onSaveAndNextStep = async () => {
    if (selectedType === 'research') {
      setSelectedType('summarized');
      if (!summary) {
        setIsLoadingSummarized(true);
        abortControllerRef.current = null;
        getDataSummarized();
      }
    } else {
      if (summary) {
        moveToNextStep();
      }
    }
  };

  const handleReGenData = async () => {
    const id: string[] = data.filter(d => d.order !== 0 && d.order !== 1).map(t => t.id);

    setIsLoadingSummarized(true);
    setMarkdownSum('');
    setScoringReportData(null);
    await updateDataStatus({
      id: stepId,
      status: EStatusTask.COMPLETED,
      stepIds: [],
      stepInfoIds: id,
      select: 'all',
      isGenerate: true,
      stepInfoIdsGenerate: [],
    });

    await queryClient.invalidateQueries({ queryKey: ['getInfoDetail', stepId], type: 'all' });
  };

  const handleDownloadFile = async () => {
    if (!currentStep) {
      return;
    }
    const nameStep = currentStep.name;
    const html = await markdownToHTMLToDocFile(selectedType === 'summarized' ? summary : markdown);

    await downloadMDToFile(html, projectName, nameStep, nameForm, selectedType);
  };

  return (

    <>
      <div className="sticky top-[70px] bg-white z-1 pb-2">
        <HeaderResearch
          // isViewCopyBox={isViewCopyBox}
          isScoringAI={selectedType === 'summarized' && !!scoringReportData}
          isViewCopyBox={false}
          isHiddenBackButton={!markdown}
          options={optionList}
          selectedType={selectedType}
          isNotAcceptEdit={isViewEditButton}
          textCopy={markdown}
          isLoading={selectedType === 'summarized' ? isLoadingSummarized : isLoading}
          dataScoring={scoringReportData}
          handleSelectedType={handleSelectType}
          onClickApprove={onSubmit}
          onSaveAndNextStep={onSaveAndNextStep}
          onBackDashboard={onBackDashboard}
          onBackUploadFile={onBackUploadFile}
          onEditData={setIsEditResearch}
          onOpenDetailScore={onOpenDetailScore}
          isShowButtonReGen={selectedType === 'summarized' && isGen}
          onReGen={handleReGenData}
          onDownloadFile={handleDownloadFile}
          isShowDownloadIcon={
            selectedType === 'summarized'
              ? (!!summary && !isLoadingSummarized)
              : (!!markdown && !isLoading)
          }
        />
      </div>

      {/* { isLoading
        ? (
            <ProjectCardSkeleton />
          )
        : ( */}
      <div className="relative">
        {selectedType === 'research'
          ? (
              isLoading
                ? <ProjectCardSkeleton />
                : <MarkdownRenderer content={markdown} />)
          : (isLoadingSummarized
              ? <ProjectCardSkeleton />
              : <MarkdownRenderer content={summary} />
            )}
      </div>
      {/* )} */}
    </>
  );
};

export default TextUploadMarkdown;
