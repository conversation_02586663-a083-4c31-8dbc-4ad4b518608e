import type { EvaluationCriteria } from './evaluation-form';
import type { ProjectCampaignEnum, TemplateFiles } from './project';

export type EvaluationData = {
  initialEvaluationData: EvaluationCriteria[];
  financialCapacitySection: EvaluationCriteria[];
  collaborationSection: EvaluationCriteria[];
  growthPotentialSection: EvaluationCriteria[];
};

export type QAStrategistOutput = {
  businessType: StrategistOutput;
  industryICPFit: StrategistOutput;
  revenueScale: StrategistOutput;
  annualMarketingBudget: StrategistOutput;
  contractType: StrategistOutput;
  paymentHistory: StrategistOutput;
  workingHistoryWithMVVGroup: StrategistOutput;
  projectFrequency: StrategistOutput;
  accessToDecisionMakers: StrategistOutput;
  decisionMakingSpeed: StrategistOutput;
  complexityOfDecisionProcess: StrategistOutput;
  marketingTeamStrength: StrategistOutput;
  willingnessToShareInformation: StrategistOutput;
  proActivenessInProjectCollaboration: StrategistOutput;
  culturalFit: StrategistOutput;
  longTermVisionCommitment: StrategistOutput;
  potentialForMultiService: StrategistOutput;
  industryInfluence: StrategistOutput;
  referralCapability: StrategistOutput;
  crossCellAndUpCell: StrategistOutput;
};

export type StrategistOutput = {
  option: string;
  sources: string[];
  confidence: string;
};

export type CreateScreeningOutcome = {
  createScores: createScores[];
  projectId: string;
};

export type createScores = {
  order: number;
  name: string;
  scores: Scores[];
};

export type Scores = {
  criteria: string;
  type: string;
  answer: string;
  confidence: string;
  citation: string;
  criteriaType: string;
  weight: string;
  criteriaScore: string;
};

export type StepInfosPayload = {
  stepInfos: TypeStepInfosRes[];
  // infos: any[];
};

export type TypeStepInfosRes = {
  order: number;
  infos: any[];
};

export type InfosPayload = {
  question: string;
  answer: string;
  id: string;
};

export enum EValueOfValuationType {
  businessType = 1,
  industryICPFit = 2,
  revenueScale = 3,
  annualMarketingBudget = 4,
  contractType = 5,
  paymentHistory = 6,
  workingHistoryWithMVVGroup = 7,
  projectFrequency = 8,
  accessToDecisionMakers = 9,
  decisionMakingSpeed = 10,
  complexityOfDecisionProcess = 11,
  marketingTeamStrength = 12,
  willingnessToShareInformation = 13,
  proActivenessInProjectCollaboration = 14,
  culturalFit = 15,
  longTermVisionCommitment = 16,
  potentialForMultiService = 17,
  crossCellAndUpCell = 18,
  industryInfluence = 19,
  referralCapability = 20,
}

export enum EType {
  businessType = 'businessType',
  industryICPFit = 'industryICPFit',
  revenueScale = 'revenueScale',
  annualMarketingBudget = 'annualMarketingBudget',
  contractType = 'contractType',
  paymentHistory = 'paymentHistory',
  workingHistoryWithMVVGroup = 'workingHistoryWithMVVGroup',
  projectFrequency = 'projectFrequency',
  accessToDecisionMakers = 'accessToDecisionMakers',
  decisionMakingSpeed = 'decisionMakingSpeed',
  complexityOfDecisionProcess = 'complexityOfDecisionProcess',
  marketingTeamStrength = 'marketingTeamStrength',
  willingnessToShareInformation = 'willingnessToShareInformation',
  proActivenessInProjectCollaboration = 'proActivenessInProjectCollaboration',
  culturalFit = 'culturalFit',
  longTermVisionCommitment = 'longTermVisionCommitment',
  potentialForMultiService = 'potentialForMultiService',
  crossCellAndUpCell = 'crossCellAndUpCell',
  industryInfluence = 'industryInfluence',
  referralCapability = 'referralCapability',
}

export type ScoreDetail = {
  id: string;
  name: string;
  order: number;
  infos: Score[];
};

export type Score = {
  criteria: string;
  type: string;
  answer: string;
  confidence: string;
  citation: string;
  criteriaType: string;
  weight: string;
  criteriaScore: string;
  convertedScore: string;
};

export type TemplatesResponse = {
  campaign: ProjectCampaignEnum;
  files: TemplateFiles[];
};
