import type { ItemFrameworkResponse } from '@/features/frameworks-templates/types';
import type { IFileResponse } from '@/shared/types/global';

export type CreateProjectResearch = {
  name: string;
  stepId: string;
  order: number;
  files?: ResearchTemplateCustomFile[];
  infos: {
    framework?: ItemFrameworkResponse | null;
    researchType?: ResearchFramework;
    template: IFileResponse[];
    files: IFileResponse[];
    otherTemplate?: string;
    frameWorkId: string;
    input?: string[];
    type?: ReportType;
    templateId?: string;
  }[];
};

export type ResearchInfo = {
  template: IFileResponse[];
  researchType?: ResearchFramework;
  framework?: ItemFrameworkResponse | null;
  files?: IFileResponse[];
  otherTemplate?: string;
  frameWorkId: string;
  input?: string[];
  type?: ReportType;
  templateId?: string;
};

export type ResearchItem = {
  id: string;
  name: string;
  order: number;
  infos: ResearchInfo[];
  stepId: string;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
};

export type ResearchQueryParams = {
  itemsPerPage?: number;
  page?: number;
  searchValue?: string;
  stepId?: string;
};

export type GetResearchResponse = {
  items: ResearchItem[];
  total: number;
  page: number;
  itemsPerPage: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
};

export type UpdateProjectResearch = {
  name: string;
  files?: ResearchTemplateCustomFile[];
  infos: {
    template: IFileResponse[];
    framework?: ItemFrameworkResponse | null;
    files?: IFileResponse[];
    otherTemplate?: string;
    frameWorkId: string;
    input?: string[];
    type?: ReportType;
    templateId?: string;
    researchType?: ResearchFramework;
  }[];
};

export enum ResearchFramework {
  DeskResearch,
  QuantitativeResearch,
  QualitativeResearch,
  ExploratoryResearch,
  DescriptiveResearch,
  ExplanatoryResearch,
  FieldResearch,
  ExperimentalResearch,
  Other,
}

export const frameworkOptions = [
  { value: ResearchFramework.DeskResearch, label: 'Desk Research', disable: false },
  { value: ResearchFramework.QuantitativeResearch, label: 'Quantitative Research', disable: false },
  { value: ResearchFramework.QualitativeResearch, label: 'Qualitative Research', disable: false },
  { value: ResearchFramework.ExploratoryResearch, label: 'Exploratory Research', disable: true },
  { value: ResearchFramework.DescriptiveResearch, label: 'Descriptive Research', disable: true },
  { value: ResearchFramework.ExplanatoryResearch, label: 'Explanatory Research', disable: true },
  { value: ResearchFramework.FieldResearch, label: 'Field Research', disable: true },
  { value: ResearchFramework.ExperimentalResearch, label: 'Experimental Research', disable: true },
  { value: ResearchFramework.Other, label: 'Other', disable: true },
];

export const CONVERT_RESEARCH_FRAMEWORK_TO_LABEL: { [key: string]: string } = {
  [ResearchFramework.DeskResearch]: 'Desk Research',
  [ResearchFramework.QualitativeResearch]: 'Qualitative Research',
  [ResearchFramework.QuantitativeResearch]: 'Quantitative Research',
  [ResearchFramework.ExploratoryResearch]: 'Exploratory Research',
  [ResearchFramework.DescriptiveResearch]: 'Descriptive Research',
  [ResearchFramework.ExplanatoryResearch]: 'Explanatory Research',
  [ResearchFramework.FieldResearch]: 'Field Research',
  [ResearchFramework.ExperimentalResearch]: 'Experimental Research',
  [ResearchFramework.Other]: 'Other',
};

// Report Type enum for document research
export enum ReportType {
  Report,
  PresentationDeck,
}

export const CONVERT_RESPORT_TYPE_TO_LABEL: { [key: string]: string } = {
  [ReportType.Report]: 'Report',
  [ReportType.PresentationDeck]: 'Presentation Deck',
};

export const reportTypeOptions = [
  { value: ReportType.Report, label: 'Report', disable: false },
  { value: ReportType.PresentationDeck, label: 'Presentation Deck', disable: true },
];

export type ResearchTemplateCustomFile = {
  originalname: string;
  mimetype: string;
  size: string;
  url: string;
  key: string;
  bucket: string;
  provider: string;
  category: string;
};
