// Type definitions for the API response
export type ScreeningCriteria = {
  id: string;
  criteria: string;
  answer: string;
  confidence: string;
  citation: string;
  criteriaType: string;
  weight: string;
  criteriaScore: string;
  convertedScore: string;
  type: string;
};

export type ScreeningOutcomeData = {
  score: number;
  percentile: number;
  rank: string;
  data: {
    clientProfileSection?: ScreeningCriteria[];
    financialCapacitySection?: ScreeningCriteria[];
    collaborationSection?: ScreeningCriteria[];
    growthPotentialSection?: ScreeningCriteria[];
    [key: string]: ScreeningCriteria[] | undefined;
  };
};

// Type definitions for chart data
export type ChartDataItem = {
  name: string;
  amount: number;
};

export type SectionChartData = {
  categories: string[];
  series: {
    name: string;
    data: number[];
  }[];
};

export type ScreeningChartData = {
  criteriaData: ChartDataItem[];
  sectionData: ChartDataItem[];
  sectionACategories: string[];
  sectionASeries: { name: string; data: number[] }[];
  sectionBCategories: string[];
  sectionBSeries: { name: string; data: number[] }[];
  sectionCCategories: string[];
  sectionCSeries: { name: string; data: number[] }[];
  sectionDCategories: string[];
  sectionDSeries: { name: string; data: number[] }[];
};
