import { ProjectCampaignEnum, ProjectStatusEnum, ProjectTypeEnum } from '../types/project';

export const PROJECT_TYPE_LABEL: { [key: string]: string } = {
  [ProjectTypeEnum.BRANDING]: 'project_type_options.branding', // ProjectTypeEnum.BRANDING
  [ProjectTypeEnum.GENERAL_CONSULTING]: 'project_type_options.general_consulting', // ProjectTypeEnum.GENERAL_CONSULTING
  [ProjectTypeEnum.DIAGNOSTICS]: 'project_type_options.diagnostics', // ProjectTypeEnum.DIAGNOSTICS
};

export const PROJECT_STATUS_LABEL: { [key: string]: string } = {
  [ProjectStatusEnum.PLANNED]: 'status_options.planned', // ProjectStatusEnum.PLANNED
  [ProjectStatusEnum.IN_PROGRESS]: 'status_options.in_progress', // ProjectStatusEnum.IN_PROGRESS
  [ProjectStatusEnum.COMPLETED]: 'status_options.completed', // ProjectStatusEnum.COMPLETED
  [ProjectStatusEnum.ON_HOLD]: 'status_options.on_hold', // ProjectStatusEnum.ON_HOLD
};

export const PROJECT_CAMPAIGN_LABEL: { [key in string]: string } = {
  [ProjectCampaignEnum.CORPORATE]: 'campaign_options.corporate',
  [ProjectCampaignEnum.CRISIS_MANAGEMENT]: 'campaign_options.crisis_management',
  [ProjectCampaignEnum.EVENT]: 'campaign_options.event',
  [ProjectCampaignEnum.GR_ADVOCACY]: 'campaign_options.gr_advocacy',
  [ProjectCampaignEnum.IMC]: 'campaign_options.imc',
  [ProjectCampaignEnum.MARKET_RESEARCH]: 'campaign_options.market_research',
  [ProjectCampaignEnum.MEDIA_RELATION_PR]: 'campaign_options.media_relation_pr',
  [ProjectCampaignEnum.MI_BRAND_BRANDING]: 'campaign_options.mi_brand_branding',
  [ProjectCampaignEnum.PRODUCT_LAUNCH]: 'campaign_options.product_launch',
  [ProjectCampaignEnum.SOCIAL_DIGITAL_CORPORATE]: 'campaign_options.social_digital_corporate',
  [ProjectCampaignEnum.SOCIAL_DIGITAL_PRODUCT]: 'campaign_options.social_digital_product',
  [ProjectCampaignEnum.TVC_VIDEO_PRODUCTION]: 'campaign_options.tvc_video_production',
};

export const DEFAULT_PAGE_SIZE = 10;
