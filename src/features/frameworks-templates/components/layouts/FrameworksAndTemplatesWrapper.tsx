import FrameworkAndTemplateManagement from '../framework-template-management/FrameworkTemplateManagement';

const FrameworksAndTemplatesWrapper: React.FC = () => {
  return (
    <div className="py-4 px-6">
      <h2 className="text-xl font-medium text-foreground">
        Framework & Templates Management
      </h2>
      <p>Create, edit, manage different framework and templates.</p>

      <div className="flex mt-4 gap-6">
        {/* <div className="flex-3 shrink-0">
          <CompanyList />
        </div> */}
        <div className="flex-10 shrink-0">
          <FrameworkAndTemplateManagement />
        </div>
      </div>
    </div>
  );
};

export default FrameworksAndTemplatesWrapper;
