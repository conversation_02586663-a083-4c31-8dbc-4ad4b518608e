'use client';

import type {
  AuthError,
  AuthOptions,
  LoginCredentials,
  User,
} from '../types/auth.types';
import type { ApiResponse } from '@/shared/types/api-response';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { useRouter } from 'next/navigation';
import { useCallback, useEffect, useMemo } from 'react';
import { toast } from 'sonner';
import { COOKIE_CURRENT_ID, COOKIE_CURRENT_PATH, COOKIE_SESSION_KEY, SESSION_EXPIRATION_SECONDS } from '../constant';
import { getCurrentUserApi, loginApi, logoutApi } from '../services/auth.service';
import { useAuthStore } from '../store/auth.store';
import { getToken, isAuthenticated, removeToken, setToken } from '../utils/token';
import Cookies from 'js-cookie';

/**
 * Default auth options
 */
const DEFAULT_AUTH_OPTIONS: AuthOptions = {
  tokenKey: COOKIE_SESSION_KEY,
  tokenExpiration: SESSION_EXPIRATION_SECONDS,
  tokenOptions: {
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'lax',
  },
  loginRedirectPath: '/dashboard/projects',
  logoutRedirectPath: '/signin',
  autoRedirect: true,
  autoRefreshToken: false,
};

/**
 * Custom hook for authentication functionality
 *
 * This hook provides a centralized way to handle authentication in the application.
 * It combines Zustand for state management and React Query for data fetching.
 *
 * @param options - Configuration options for the auth hook
 * @returns Authentication methods and state
 */
export function useAuth(options: Partial<AuthOptions> = {}) {
  // Merge default options with provided options
  const authOptions = useMemo(() => {
    return { ...DEFAULT_AUTH_OPTIONS, ...options };
  }, [options]);

  // Get router for navigation
  const router = useRouter();

  // Get query client for cache management
  const queryClient = useQueryClient();

  // Get auth store state and actions
  const {
    user,
    isAuthenticated: isAuthenticatedState,
    isLoading,
    error,
    setUser,
    setAuthenticated,
    setLoading,
    setError,
    reset: resetAuthStore,
  } = useAuthStore();

  /**
   * Check if user is authenticated based on token presence
   */
  const checkAuthentication = useCallback(() => {
    const authenticated = isAuthenticated(authOptions.tokenKey);
    setAuthenticated(authenticated);
    return authenticated;
  }, [authOptions.tokenKey, setAuthenticated]);

  /**
   * Handle authentication errors
   */
  const handleAuthError = useCallback((error: AuthError) => {
    setError(error);

    // Show toast notification for errors
    if (error.message) {
      toast.error(error.message);
    }

    // Handle session expiration
    if (error.type === 'SESSION_EXPIRED' || error.type === 'UNAUTHORIZED') {
      // Clear auth state and redirect to login
      removeToken(authOptions.tokenKey);
      resetAuthStore();

      // Redirect to login page
      if (authOptions.autoRedirect && authOptions.logoutRedirectPath) {
        router.push(authOptions.logoutRedirectPath);
      }
    }

    return error;
  }, [setError, authOptions, router, resetAuthStore]);

  /**
   * Query for fetching the current user profile
   */
  const {
    refetch: refetchUser,
  } = useQuery({
    queryKey: ['auth', 'user'],
    queryFn: async () => {
      try {
        // Skip if not authenticated
        if (!checkAuthentication()) {
          return { data: null } as ApiResponse<User>;
        }

        setLoading(true);
        const response = await getCurrentUserApi();

        if (response.data) {
          setUser(response.data);
        }

        return response;
      } catch (error: any) {
        handleAuthError(error);
        throw error;
      } finally {
        setLoading(false);
      }
    },
    enabled: isAuthenticatedState, // Only run if authenticated
    retry: 1,
  });

  /**
   * Login mutation
   */
  const loginMutation = useMutation({
    mutationFn: async (credentials: LoginCredentials) => {
      setLoading(true);
      setError(null);

      try {
        const response = await loginApi(credentials);

        if (response.data?.accessToken) {
          // Store token in cookies
          setToken(
            response.data.accessToken,
            authOptions.tokenOptions,
            authOptions.tokenKey,
          );

          // Update auth state
          setAuthenticated(true);

          // If user data is included in the response, set it
          if (response.data.user) {
            setUser(response.data.user);
          } else {
            // Otherwise fetch user data
            await refetchUser();
          }

          // Show success message
          toast.success('Login successful');
          const currentPath = Cookies.get(COOKIE_CURRENT_PATH);
          const id = Cookies.get(COOKIE_CURRENT_ID);
          if (authOptions.autoRedirect && currentPath && id) {
            const path = `${decodeURIComponent(currentPath)}?id=${id}`;
            router.push(path);
          } else if (authOptions.autoRedirect && authOptions.loginRedirectPath) {
            // Auto redirect if enabled
            router.push(authOptions.loginRedirectPath);
          }
        }

        return response;
      } catch (error: any) {
        setLoading(false);
        throw error;
      }
    },
  });

  /**
   * Logout mutation
   */
  const logoutMutation = useMutation({
    mutationFn: async () => {
      setLoading(true);

      try {
        // Call logout API
        await logoutApi();

        // Clear token
        removeToken(authOptions.tokenKey);

        // Reset auth store
        resetAuthStore();

        // Clear React Query cache
        queryClient.clear();

        // Show success message
        toast.success('Logged out successfully');

        // Auto redirect if enabled
        if (authOptions.autoRedirect && authOptions.logoutRedirectPath) {
          router.push(authOptions.logoutRedirectPath);
        }
      } catch (error: any) {
        // Even if the API call fails, we still want to clear local state
        removeToken(authOptions.tokenKey);
        resetAuthStore();
        queryClient.clear();

        // Log the error but don't show it to the user
        console.error('Logout API error:', error);
      } finally {
        setLoading(false);
      }
    },
  });

  /**
   * Login function
   */
  const login = useCallback(async (credentials: LoginCredentials) => {
    return loginMutation.mutateAsync(credentials);
  }, [loginMutation]);

  /**
   * Logout function
   */
  const logout = useCallback(() => {
    return logoutMutation.mutateAsync();
  }, [logoutMutation]);

  /**
   * Check authentication status on mount
   */
  useEffect(() => {
    const isUserAuthenticated = checkAuthentication();

    // If authenticated but no user data, fetch it
    if (isUserAuthenticated && !user) {
      refetchUser().catch(console.error);
    }
  }, [checkAuthentication, refetchUser, user]);

  // Return auth state and methods
  return {
    // State
    user,
    isAuthenticated: isAuthenticatedState,
    isLoading,
    error,

    // Methods
    login,
    logout,
    refetchUser,

    // Utility methods
    getToken: () => getToken(authOptions.tokenKey),
    checkAuthentication,
  };
}
