{"RootLayout": {"home_link": "Home", "about_link": "About", "counter_link": "Counter", "portfolio_link": "Portfolio", "sign_in_link": "Sign in", "sign_up_link": "Sign up"}, "BaseTemplate": {"description": "Starter code for your Nextjs Boilerplate with Tailwind CSS", "made_with": "Made with <author></author>."}, "Index": {"meta_title": "Next.js Boilerplate Presentation", "meta_description": "Next js Boilerplate is the perfect starter code for your project. Build your React application with the Next.js framework.", "sponsors_title": "Sponsors"}, "Counter": {"meta_title": "Counter", "meta_description": "An example of DB operation", "loading_counter": "Loading counter...", "security_powered_by": "Security, bot detection and rate limiting powered by"}, "CounterForm": {"presentation": "The counter is stored in the database and incremented by the value you provide.", "label_increment": "Increment by", "button_increment": "Increment"}, "CurrentCount": {"count": "Count: {count}"}, "About": {"meta_title": "About", "meta_description": "About page description", "about_paragraph": "Welcome to our About page! We are a team of passionate individuals dedicated to creating amazing software.", "translation_powered_by": "Translation powered by"}, "Portfolio": {"meta_title": "Portfolio", "meta_description": "Welcome to my portfolio page!", "presentation": "Welcome to my portfolio page! Here you will find a carefully curated collection of my work and accomplishments. Through this portfolio, I'm to showcase my expertise, creativity, and the value I can bring to your projects.", "portfolio_name": "Portfolio {name}", "error_reporting_powered_by": "Error reporting powered by", "coverage_powered_by": "Code coverage powered by"}, "PortfolioSlug": {"meta_title": "Portfolio {slug}", "meta_description": "Portfolio {slug} description", "header": "Portfolio {slug}", "content": "Created a set of promotional materials and branding elements for a corporate event. Crafted a visually unified theme, encompassing a logo, posters, banners, and digital assets. Integrated the client's brand identity while infusing it with a contemporary and innovative approach. Garnered favorable responses from event attendees, resulting in a successful event with heightened participant engagement and increased brand visibility.", "code_review_powered_by": "Code review powered by"}, "SignIn": {"meta_title": "Sign in", "meta_description": "Seamlessly sign in to your account with our user-friendly login process."}, "SignUp": {"meta_title": "Sign up", "meta_description": "Effortlessly create an account through our intuitive sign-up process."}, "Dashboard": {"meta_title": "Dashboard", "hello_message": "Hello {email}!", "alternative_message": "Want to build your SaaS faster using the same stack? Try <url></url>."}, "UserProfile": {"meta_title": "User Profile"}, "DashboardLayout": {"dashboard_link": "Dashboard", "user_profile_link": "Manage your account", "sign_out": "Sign out"}, "TaskWorkflow": {"mvv_home": "MVV Home", "chat_ai": "Chat with AI"}, "Project": {"create_new_project_button": "New Project", "form_title": "Create New Project", "form_title_detail": "Project Detail", "form_subtitle": "Fill in the details to create a new project", "client_section": "Client Information", "client_name": "Client Name", "address": "Address", "tax_code": "Tax Code", "contact_person": "Contact Person", "tel": "Telephone", "email": "Email", "industry": "Industry", "project_section": "Project Information", "project_name": "Project Name", "project_type": "Project Type", "campaign": "Campaign", "type": "Type", "description": "Description", "status": "Status", "start_date": "Start Date", "end_date": "End Date", "project_owner": "Project Owner", "team_members": "Team Members", "required_fields_error": "Please fill in all required fields: {fields}", "success_message": "Project created successfully!", "error_message": "Failed to create project. Please try again.", "cancel_button": "Cancel", "back_button": "Back", "create_button": "Create Project", "edit_button": "Edit Project", "creating_button": "Creating...", "update_button": "Update Project", "updating_button": "Updating...", "placeholder": {"client_name": "Enter client name", "address": "Enter address", "tax_code": "Enter tax code", "contact_person": "Enter contact person name", "tel": "Enter telephone number", "email": "Enter email address", "industry": "Enter industry", "project_name": "Enter project name", "project_type": "Select project type", "campaign": "Select campaign", "description": "Enter project description", "status": "Select status", "start_date": "Pick a date", "end_date": "Pick a date", "project_owner": "Select project owner", "select_team_members": "Select team members", "search_team_members": "Search team members...", "select_report_input": "Select multiple", "search_research": "Search research..."}, "project_type_options": {"branding": "Branding", "general_consulting": "General Consulting", "diagnostics": "Diagnostics"}, "campaign_options": {"corporate": "Corporate", "crisis_management": "Crisis Management", "event": "Event", "gr_advocacy": "GR-Advocacy", "imc": "IMC", "market_research": "Market Research", "media_relation_pr": "Media Relation - PR", "mi_brand_branding": "Mibrand Branding", "product_launch": "Product Launch", "social_digital_corporate": "Social & Digital Corporate", "social_digital_product": "Social Media & Digital Product", "tvc_video_production": "TVC/Video Production"}, "status_options": {"planned": "Planned", "in_progress": "In Progress", "completed": "Completed", "on_hold": "On Hold"}, "delete_project": "Delete project", "confirm_delete_project": "Are you sure you want to delete this project? This action cannot be undone.", "confirm_delete_project_title": "Delete Project", "project_deleted_successfully": "Project deleted successfully", "error_deleting_project": "Error deleting project. Please try again.", "cancel": "Cancel", "delete": "Delete", "deleting": "Deleting...", "search_projects": "Search projects...", "filter_projects": "Filter Projects", "all_statuses": "All Statuses", "all_types": "All Types", "all_campaigns": "All Campaigns", "clear_all_filters": "Clear All Filters", "remove_status_filter": "Remove status filter", "remove_type_filter": "Remove type filter", "remove_campaign_filter": "Remove campaign filter", "remove_start_date_filter": "Remove start date filter", "remove_end_date_filter": "Remove end date filter", "remove_search_filter": "Remove search filter", "status_filter": "Status: {status}", "type_filter": "Type: {type}", "campaign_filter": "Campaign: {campaign}", "start_date_filter": "Start Date: {date}", "end_date_filter": "End Date: {date}", "search_filter": "Search: {query}", "search": "Search", "edit": "Edit", "create": "Create", "update": "Update", "creating": "Creating...", "updating": "Updating...", "research_name": "Research name", "research_framework": "Research framework", "research_template": "Research template", "create_research": "Create Research", "edit_research": "Edit Research", "update_research": "Update Research", "delete_research": "Delete Research", "research_form_description": "Please enter all the required information to proceed.", "search_research_placeholder": "Search research analysis", "confirm_delete_research": "Are you sure you want to delete \"{name}\"? This action cannot be undone.", "select_framework": "Select framework", "select_template": "Select template", "search_frameworks": "Search frameworks...", "search_templates": "Search templates...", "no_research_items": "No research items found. Create your first research item to get started.", "report_input": "Report input", "report_type": "Report type", "select_report_type": "Select", "report_name": "Report name", "research_type": "Research type"}, "TeamMember": {"team_members": "Team Members", "create_new_user_button": "New Member", "form_title": "Add New Team Member", "form_subtitle": "Fill in the details to add a new team member", "user_information": "User Information", "first_name": "First Name", "last_name": "Last Name", "email": "Email", "password": "Password", "roles": "Roles", "required_fields_error": "Please fill in all required fields: {fields}", "success_message": "Team member created successfully!", "error_message": "Failed to create team member. Please try again.", "cancel_button": "Cancel", "create_button": "Create Team Member", "creating_button": "Creating...", "placeholder": {"first_name": "Enter first name", "last_name": "Enter last name", "email": "Enter email address", "password": "Enter password", "roles": "Select roles"}, "role_options": {"user": "User", "admin": "Admin", "manager": "Manager"}, "delete_user": "Delete team member", "confirm_delete_user": "Are you sure you want to delete <name></name>? This action cannot be undone.", "confirm_delete_user_title": "Delete Team Member", "user_deleted_successfully": "Team member deleted successfully", "error_deleting_user": "Error deleting team member. Please try again.", "cancel": "Cancel", "delete": "Delete", "deleting": "Deleting...", "search_team_members": "Search team members...", "no_team_members_found": "No team members found.", "loading_more_team_members": "Loading more team members...", "try_again": "Try Again", "table": {"no": "No.", "full_name": "Full Name", "email": "Email", "updated_at": "Updated At", "actions": "Actions"}}, "Prompt": {"prompts": "Prompts", "create_new_prompt_button": "New Prompt", "form_title": "Add New Prompt", "form_subtitle": "Fill in the details to add a new prompt", "name": "Name", "description": "Description", "content": "Content", "required_fields_error": "Please fill in all required fields: {fields}", "success_message": "Prompt created successfully!", "error_message": "Failed to create prompt. Please try again.", "cancel_button": "Cancel", "create_button": "Create Prompt", "creating_button": "Creating...", "placeholder": {"name": "Enter prompt name", "description": "Enter prompt description", "content": "Enter prompt content"}, "delete_prompt": "Delete prompt", "confirm_delete_prompt": "Are you sure you want to delete <name></name>? This action cannot be undone.", "confirm_delete_prompt_title": "Delete Prompt", "prompt_deleted_successfully": "Prompt deleted successfully", "error_deleting_prompt": "Error deleting prompt. Please try again.", "update_form_title": "Update Prompt", "update_form_subtitle": "Update the details of this prompt.", "prompt_information": "Prompt Information", "update_button": "Update", "updating_button": "Updating...", "update_prompt": "Update Prompt", "prompt_updated_successfully": "Prompt updated successfully", "error_updating_prompt": "Error updating prompt. Please try again.", "cancel": "Cancel", "delete": "Delete", "deleting": "Deleting...", "search_prompts": "Search prompts...", "no_prompts_found": "No prompts found.", "loading_more_prompts": "Loading more prompts...", "try_again": "Try Again", "table": {"no": "No.", "name": "Name", "description": "Description", "content": "Content", "updated_at": "Updated At", "actions": "Actions"}}, "Step": {"steps_title": "Steps", "steps_description": "Manage workflow steps and their associated prompts", "order": "Order", "name": "Name", "description": "Description", "prompts": "Prompts", "prompt": "prompt", "prompts_in_step": "Prompts in this step", "add_prompts": "Add Prompts", "no_prompts_in_step": "No prompts in this step yet", "click_add_prompts": "Click \"Add Prompts\" to get started", "add_prompts_to_step": "Add Prompts to Step", "add_prompts_description": "Select prompts to add to step {stepName}", "search_prompts": "Search prompts...", "select_all": "Select All", "deselect_all": "Deselect All", "selected": "selected", "loading_prompts": "Loading prompts...", "no_prompts_found": "No prompts found matching your search", "no_available_prompts": "No available prompts to add", "cancel": "Cancel", "add_selected_prompts": "Add {count} Prompts", "adding_prompts": "Adding...", "search_steps": "Search steps...", "error_loading_steps": "Error loading steps", "error_generic": "Something went wrong. Please try again.", "no_steps_found": "No steps found", "no_steps_description": "Steps will appear here once they are created.", "loading_more": "Loading more steps...", "load_more": "Load more steps", "parent_step_no_prompts": "Parent steps with child steps do not contain prompts.", "prompts_in_child_steps": "Prompts are managed in the child steps below."}}