@import 'tailwindcss';
@import 'tw-animate-css';

/**
 * Theme styles
 */
@import './themes/surface.css';

/**
 * Base styles
 */
@import './base/variables.css';
@import './base/animations.css';
@import './base/utilities.css';
@import './base/typography.css';

/**
 * Component styles
 */
@import './components/menu.css';
@import './components/scrollbar.css';
@import './components/forms.css';
@import './components/tasks.css';

/**
 * Third-party libraries
 */
@import './vendors/apexcharts.css';
@import './vendors/flatpickr.css';
@import './vendors/fullcalendar.css';
@import './vendors/swiper.css';
@import './vendors/jvectormap.css';
@import './vendors/joditEditor.css';

.copilotKitMessages {
  max-height: calc(100vh - 333px);
  min-height: calc(100vh - 333px);
}
