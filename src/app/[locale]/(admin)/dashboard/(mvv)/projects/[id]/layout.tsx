import { ProjectWorkflowStoreProvider } from '@/features/project-management/stores/project-workflow-store';
import { AGENT_ROUTE_NAME } from '@/shared/constants/global';
import { CopilotKit } from '@copilotkit/react-core';
import '@copilotkit/react-ui/styles.css';

export default async function ProjectDetailLayout({
  children,
  params,
}: {
  children: React.ReactNode;
  params: Promise<{ id: string }>;
}) {
  const { id } = await params;

  return (
    <CopilotKit agent={AGENT_ROUTE_NAME} runtimeUrl="/api/copilotkit">
      <ProjectWorkflowStoreProvider projectId={id}>
        {children}
      </ProjectWorkflowStoreProvider>
    </CopilotKit>
  );
}
