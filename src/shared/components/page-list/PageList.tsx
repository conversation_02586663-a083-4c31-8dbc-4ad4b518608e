'use client';

import type { VariantProps } from 'class-variance-authority';
import type { ReactNode } from 'react';
import { AlertCircle, Filter, Search } from 'lucide-react';
import { useEffect } from 'react';
import { useInView } from 'react-intersection-observer';
import { Button } from '@/shared/components/ui/button';
import Input from '@/shared/components/form/input/InputField';
import Pagination from '@/shared/components/tables/Pagination';
import { cn } from '@/shared/utils/utils';
import {
  pageListContainerVariants,
  pageListContentVariants,
  pageListEmptyMessageVariants,
  pageListErrorContainerVariants,
  pageListErrorIconVariants,
  pageListErrorMessageVariants,
  pageListGridVariants,
  pageListLoadingMessageVariants,
} from './page-list-variants';

export type PageListProps = {
  // Content
  children: ReactNode;
  emptyMessage?: string;
  loadingMessage?: string;
  description?: string;

  // Search and Filter
  searchValue?: string;
  onSearchChange?: (value: string) => void;
  onFilterClick?: () => void;
  showSearch?: boolean;
  showFilter?: boolean;
  searchPlaceholder?: string;

  // Create New
  onCreateNew?: () => void;
  createNewLabel?: string;
  showCreateNew?: boolean;
  createNewCard?: ReactNode;

  // State
  isLoading?: boolean;
  isError?: boolean;
  error?: Error | null;
  isEmpty?: boolean;

  // Pagination (both infinite scroll and traditional)
  hasNextPage?: boolean;
  isFetchingNextPage?: boolean;
  onLoadMore?: () => void;

  // Traditional pagination
  currentPage?: number;
  totalPages?: number; // Deprecated: use total and itemsPerPage instead
  onPageChange?: (page: number) => void;
  usePagination?: boolean;

  // New pagination props
  total?: number; // Total number of items
  itemsPerPage?: number; // Items per page

  // Total count
  totalCount?: number;
  showTotal?: boolean;

  // Actions
  onRetry?: () => void;

  // Styling
  variant?: VariantProps<typeof pageListContainerVariants>['variant'];
  gridCols?: VariantProps<typeof pageListGridVariants>['cols'];
  className?: string;

  // Skeleton/Loading
  skeletonComponent?: ReactNode;
  skeletonCount?: number;
};

/**
 * PageList Component
 *
 * A reusable list component that handles:
 * - Flexible card display with customizable grid layout
 * - Loading states with skeleton components
 * - Error handling with retry functionality
 * - Empty states
 * - Infinite scrolling pagination
 * - All actions are handled externally via props
 *
 * This component uses semantic color tokens from our theming system.
 */
export function PageList({
  children,
  emptyMessage = 'No items found.',
  loadingMessage = 'Loading more items...',
  description,

  // Search and Filter
  searchValue = '',
  onSearchChange,
  onFilterClick,
  showSearch = false,
  showFilter = false,
  searchPlaceholder = 'Search...',

  // Create New
  showCreateNew = false,
  createNewCard,

  // State
  isLoading = false,
  isError = false,
  error = null,
  isEmpty = false,

  // Pagination
  hasNextPage = false,
  isFetchingNextPage = false,
  onLoadMore,
  currentPage = 1,
  totalPages = 1,
  onPageChange,
  usePagination = false,

  // New pagination props
  total,
  itemsPerPage,

  // Total count
  totalCount = 0,
  showTotal = false,

  // Actions
  onRetry,

  // Styling
  variant = 'default',
  gridCols = 'responsive',
  className,

  // Skeleton/Loading
  skeletonComponent,
  skeletonCount = 4,
}: PageListProps) {
  // Set up intersection observer for infinite scrolling
  const { ref, inView } = useInView({
    threshold: 0,
    rootMargin: '100px',
  });

  // Load more items when the user scrolls to the bottom
  useEffect(() => {
    if (inView && hasNextPage && !isFetchingNextPage && onLoadMore) {
      onLoadMore();
    }
  }, [inView, hasNextPage, isFetchingNextPage, onLoadMore]);

  // Calculate totalPages from total and itemsPerPage if provided
  const calculatedTotalPages = total !== undefined && itemsPerPage !== undefined && itemsPerPage > 0
    ? Math.ceil(total / itemsPerPage)
    : totalPages;

  return (
    <div className={cn(pageListContainerVariants({ variant }), className)}>
      {/* Built-in header with search, filter, and create new */}
      {(showSearch || showFilter || showTotal) && (
        <div className="px-6 py-4 bg-background">
          <div className="flex flex-col gap-4">
            {/* Search and filter controls */}
            {(showSearch || showFilter) && (
              <div className="flex gap-2 items-center justify-between">
                {showSearch && (
                  <div className="relative flex-1">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                    <Input
                      placeholder={searchPlaceholder}
                      value={searchValue}
                      onChange={(e: React.ChangeEvent<HTMLInputElement>) => onSearchChange?.(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                )}
                {showFilter && (
                  <Button variant="outline" className="gap-2 h-[44px]" onClick={onFilterClick}>
                    <Filter className="w-4 h-4" />
                  </Button>
                )}
              </div>
            )}
          </div>
        </div>
      )}

      {/* Title and description */}
      {(showTotal) && (
        <div className="flex justify-between items-start px-8">
          <div>
            {(description) && (
              <p className="text-muted-foreground text-[12px] leading-[16px] m-0">
                {description}
              </p>
            )}
          </div>
          <div>
            {(showTotal) && (
              <p className="text-muted-foreground text-[12px] leading-[16px] m-0">
                {showTotal && `${totalCount} total`}
              </p>
            )}
          </div>
        </div>
      )}
      {/* Content area */}
      <div className={cn(pageListContentVariants())}>
        {isError
          ? (
              <div className={cn(pageListErrorContainerVariants({ variant }))}>
                <AlertCircle className={cn(pageListErrorIconVariants({ variant }))} />
                <p className={cn(pageListErrorMessageVariants({ variant }))}>
                  {error?.message || 'An error occurred while loading items.'}
                </p>
                {onRetry
                  && (
                    <Button onClick={onRetry} variant="outline">
                      Try Again
                    </Button>
                  )}
              </div>
            )
          : isLoading
            ? (
                <div className={cn(pageListGridVariants({ cols: gridCols }))}>
                  {/* Create new card skeleton if enabled */}
                  {showCreateNew && createNewCard && (
                    <div key="create-new-skeleton">
                      {skeletonComponent}
                    </div>
                  )}
                  {skeletonComponent && Array.from({ length: skeletonCount }).map((_, index) => (
                    <div key={`skeleton-${index}`}>
                      {skeletonComponent}
                    </div>
                  ))}
                </div>
              )
            : isEmpty
              ? (
                  <div className={cn(pageListEmptyMessageVariants({ variant }))}>
                    <p>{emptyMessage}</p>
                  </div>
                )
              : (
                  <>
                    <div className={cn(pageListGridVariants({ cols: gridCols }))}>
                      {/* Create new card */}
                      {showCreateNew && createNewCard && createNewCard}

                      {children}

                      {/* Loading more skeletons for infinite scroll */}
                      {!usePagination && isFetchingNextPage && skeletonComponent
                        && Array.from({ length: skeletonCount }).map((_, index) => (
                          <div key={`loading-skeleton-${index}`}>
                            {skeletonComponent}
                          </div>
                        ))}
                    </div>

                    {/* Pagination or infinite scroll */}
                    {usePagination && calculatedTotalPages > 1 && onPageChange
                      ? (
                          <div className="flex justify-center mt-6">
                            <Pagination
                              currentPage={currentPage}
                              totalPages={calculatedTotalPages}
                              onPageChange={onPageChange}
                            />
                          </div>
                        )
                      : (
                          /* Intersection observer target for infinite scrolling */
                          hasNextPage && (
                            <div
                              ref={ref}
                              className="h-20 flex items-center justify-center mt-4"
                            >
                              {isFetchingNextPage && (
                                <div className={cn(pageListLoadingMessageVariants({ variant }))}>
                                  {loadingMessage}
                                </div>
                              )}
                            </div>
                          )
                        )}
                  </>
                )}
      </div>
    </div>
  );
}
