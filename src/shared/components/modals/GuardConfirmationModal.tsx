'use client';

import { Modal } from '@/shared/components/ui/modal';
import { Button } from '@/shared/components/ui/button';
import { AlertTriangle } from 'lucide-react';

type ConfirmationDialogProps = {
  /** Whether the modal is open */
  open: boolean;
  /** Callback when the modal open state changes */
  onOpenChange: (open: boolean) => void;
  /** Title of the confirmation dialog */
  title?: string;
  /** Description/message to display */
  description?: string;
  /** Callback when user confirms the action */
  onConfirm: () => void;
  /** Callback when user cancels the action */
  onCancel: () => void;
  /** Text for the confirm button */
  confirmText?: string;
  /** Text for the cancel button */
  cancelText?: string;
  /** Visual variant of the modal */
  variant?: 'warning' | 'danger';
};

/**
 * GuardConfirmationModal Component
 *
 * A reusable confirmation modal for warning users about unsaved changes
 * when they attempt to navigate away or perform destructive actions.
 *
 * @example
 * ```tsx
 * const [showModal, setShowModal] = useState(false);
 *
 * <GuardConfirmationModal
 *   open={showModal}
 *   onOpenChange={setShowModal}
 *   title="Unsaved Changes"
 *   description="You have unsaved changes. Are you sure you want to leave?"
 *   onConfirm={() => {
 *     // Handle navigation or action
 *     router.push('/other-page');
 *   }}
 *   onCancel={() => {
 *     // Handle cancel action
 *     setShowModal(false);
 *   }}
 *   variant="warning"
 * />
 * ```
 */
export function GuardConfirmationModal({
  open,
  onOpenChange,
  title = 'Unsaved Changes',
  description = 'You have unsaved changes. Are you sure you want to leave? Your changes will be lost.',
  onConfirm,
  onCancel,
  confirmText = 'Leave anyway',
  cancelText = 'Stay',
  variant = 'warning',
}: ConfirmationDialogProps) {
  const handleClose = () => {
    onOpenChange(false);
    onCancel();
  };

  const handleConfirm = () => {
    onConfirm();
    onOpenChange(false);
  };

  return (
    <Modal
      isOpen={open}
      onClose={handleClose}
      className="max-w-md"
      showCloseButton={false}
    >
      <div className="p-6">
        {/* Header with Icon */}
        <div className="flex items-start gap-4 mb-4">
          <div className={`flex-shrink-0 w-10 h-10 rounded-full flex items-center justify-center ${
            variant === 'danger'
              ? 'bg-destructive/10 text-destructive'
              : 'bg-warning/10 text-warning'
          }`}
          >
            <AlertTriangle className="w-5 h-5" />
          </div>
          <div className="flex-1">
            <h3 className="text-lg font-semibold text-foreground mb-2">
              {title}
            </h3>
            <p className="text-sm text-muted-foreground leading-relaxed">
              {description}
            </p>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex items-center justify-end gap-3 mt-6">
          <Button
            variant="outline"
            onClick={handleClose}
            className="min-w-[80px]"
          >
            {cancelText}
          </Button>
          <Button
            variant={variant === 'danger' ? 'destructive' : 'default'}
            onClick={handleConfirm}
            className="min-w-[80px]"
          >
            {confirmText}
          </Button>
        </div>
      </div>
    </Modal>
  );
}

// Alias export for backward compatibility
export const ConfirmationDialog = GuardConfirmationModal;
