'use client';

import React, { useEffect, useRef, useState } from 'react';
import { useLocale } from 'next-intl';
import { useRouter } from 'next/navigation';
import { routing, usePathname } from '@/core/config/i18nNavigation';

// Language configuration with flags and names
const languages = {
  en: {
    code: 'en',
    name: 'English',
    flag: '🇺🇸',
    displayName: 'English (EN)',
  },
  fr: {
    code: 'fr',
    name: 'Français',
    flag: '🇫🇷',
    displayName: 'Français (FR)',
  },
  vi: {
    code: 'vi',
    name: 'Tiếng Việt',
    flag: '🇻🇳',
    displayName: 'Tiếng Việt (VI)',
  },
};

export const LanguageDropdown: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const router = useRouter();
  const pathname = usePathname();
  const locale = useLocale();

  const currentLanguage = languages[locale as keyof typeof languages] || languages.en;

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleLanguageChange = (languageCode: string) => {
    router.push(`/${languageCode}${pathname}`);
    router.refresh();
    setIsOpen(false);
  };

  return (
    <div className="relative" ref={dropdownRef}>
      {/* Dropdown Trigger */}
      <button
        type="button"
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center gap-2 px-3 py-2 text-sm font-medium text-foreground bg-background border border-border rounded-lg hover:bg-accent focus:outline-none focus:ring-2 focus:ring-primary/20 transition-colors"
        aria-label="Select language"
        aria-expanded={isOpen}
        aria-haspopup="true"
      >
        <span className="text-base">{currentLanguage.flag}</span>
        <span className="hidden sm:inline">{locale.toUpperCase()}</span>
        <svg
          className={`w-4 h-4 transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`}
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
        </svg>
      </button>

      {/* Dropdown Menu */}
      {isOpen && (
        <div className="absolute right-0 z-50 mt-2 w-48 bg-background border border-border rounded-lg shadow-lg">
          <div className="py-1">
            {routing.locales.map((languageCode) => {
              const language = languages[languageCode as keyof typeof languages];
              if (!language) {
                return null;
              }

              const isSelected = languageCode === locale;

              return (
                <button
                  type="button"
                  key={languageCode}
                  onClick={() => handleLanguageChange(languageCode)}
                  className={`w-full flex items-center gap-3 px-4 py-2 text-sm text-left hover:bg-accent transition-colors ${
                    isSelected ? 'bg-accent text-primary' : 'text-foreground'
                  }`}
                >
                  <span className="text-base">{language.flag}</span>
                  <span className="flex-1">{language.displayName}</span>
                  {isSelected && (
                    <svg
                      className="w-4 h-4 text-primary"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path
                        fillRule="evenodd"
                        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                        clipRule="evenodd"
                      />
                    </svg>
                  )}
                </button>
              );
            })}
          </div>
        </div>
      )}
    </div>
  );
};
