'use client';
import Image from 'next/image';
import React, { useState } from 'react';
import { useAuth } from '@/features/auth/hooks/useAuth';
import { CogIcon, SignOutIcon } from '@/shared/icons';
import { Dropdown } from '../ui/dropdown/Dropdown';
import { DropdownItem } from '../ui/dropdown/DropdownItem';

export default function UserDropdown() {
  const [isOpen, setIsOpen] = useState(false);
  const { user, logout, isLoading } = useAuth();

  function toggleDropdown(e: React.MouseEvent<HTMLButtonElement, MouseEvent>) {
    e.stopPropagation();
    setIsOpen(prev => !prev);
  }

  function closeDropdown() {
    setIsOpen(false);
  }

  async function handleSignOut(e: React.MouseEvent) {
    e.preventDefault();
    closeDropdown();

    try {
      // Use the logout method from our auth hook
      await logout();
      // The hook handles token removal and redirects
    } catch (error) {
      console.error('Logout failed:', error);
    }
  }
  return (
    <div className="relative">
      <button
        type="button"
        onClick={toggleDropdown}
        className="flex items-center text-foreground dropdown-toggle"
      >
        <span className="mr-3 overflow-hidden rounded-full h-11 w-11">
          <Image
            width={44}
            height={44}
            src="/images/user/user-01.jpg"
            alt="User"
          />
        </span>

        <span className="block mr-1 font-medium text-theme-sm">
          {user?.firstName || user?.email?.split('@')[0] || 'User'}
        </span>

        <svg
          className={`stroke-muted-foreground transition-transform duration-200 ${
            isOpen ? 'rotate-180' : ''
          }`}
          width="18"
          height="20"
          viewBox="0 0 18 20"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M4.3125 8.65625L9 13.3437L13.6875 8.65625"
            stroke="currentColor"
            strokeWidth="1.5"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
        </svg>
      </button>

      <Dropdown
        isOpen={isOpen}
        onClose={closeDropdown}
        className="absolute right-0 mt-[17px] flex w-[260px] flex-col rounded-2xl border border-border bg-card p-3 shadow-theme-lg"
      >
        <div>
          <span className="block font-medium text-foreground text-theme-sm">
            {user?.firstName && user?.lastName
              ? `${user.firstName} ${user.lastName}`
              : user?.email?.split('@')[0] || 'User'}
          </span>
          <span className="mt-0.5 block text-theme-xs text-muted-foreground">
            {user?.email || 'No email available'}
          </span>
        </div>

        <ul className="flex flex-col gap-1 pt-4 pb-3 border-b border-border">
          <li>
            <DropdownItem
              onItemClick={closeDropdown}
              tag="a"
              href="/dashboard/profile"
              className="flex items-center gap-3 px-3 py-2 font-medium text-foreground rounded-lg group text-theme-sm hover:bg-accent"
            >
              <CogIcon className="size-6" />
              Account settings
            </DropdownItem>
          </li>
        </ul>
        <button
          type="button"
          onClick={handleSignOut}
          className="flex items-center gap-3 px-3 py-2 mt-3 font-medium text-foreground rounded-lg group text-theme-sm hover:bg-accent w-full text-left"
          disabled={isLoading}
        >
          <SignOutIcon className="size-6" />
          Sign out
        </button>
      </Dropdown>
    </div>
  );
}
