export enum ENameStateAgentCopilotkit {
  SUMMARIZE = 'client_summarize_state',
  ASSESSMENT = 'client_assessment_state',
  ANALYSIS = 'brief_analysis_state',
  EDITING = 'content_editing_state',
  SCOPE = 'sow_analysis_state',
  QUOTATION = 'quotation_analysis_state',
};

export enum EEndpointApiCopilotkit {
  SUMMARIZE = 'client-summarize',
  ASSESSMENT = 'client-assessment',
  ANALYSIS = 'brief-analysis',
  EDITING = 'content_editing_state',
  SCOPE = 'sow-analysis',
  QUOTATION = 'quotation-analysis',
  ANSWER = 'answer-question-analysis',
  QUANTITY = 'generate-quantity-questionnaire',
  QUALITY = 'generate-quality-questionnaire',
  SUMMARY_QUANTITY = 'summary-quantity-questionnaire',
  SUMMARY_QUALITY = 'summary-quality-questionnaire',
  DESK_RESEARCH_ANSWER_QUESTION = 'desk-research-answer-question',
  DESK_RESEARCH_REPORT = 'desk-research-report',
  REPORT_QUANTITY = 'quantity-questionnaire-report',
  REPORT_QUALITY = 'quality-questionnaire-report',
  SUMMARY_REPORT = 'summary-report',
  TEMPLATE_GENERATE = 'template-generate',
  SCORING_CONTENT = 'scoring-content',
}
