stages:
  - deploy

deploy:
  stage: deploy
  needs: []
  variables:
    CONTAINER_NAME: mvv-ui_UAT
    DOCKER_IMAGE_NAME: mvv/ui:UAT
  before_script:
    # Clean up any existing container and image
    - cp $UAT_ENV .env
  script:
    - docker build -f .devops/docker/Dockerfile -t $DOCKER_IMAGE_NAME-dry-run .
    - docker rm -f $CONTAINER_NAME || true
    - docker image rm $DOCKER_IMAGE_NAME || true
    - docker tag $DOCKER_IMAGE_NAME-dry-run $DOCKER_IMAGE_NAME
    - docker run -d --name $CONTAINER_NAME --restart unless-stopped -p $UAT_PORT:3000 $DOCKER_IMAGE_NAME
  tags:
    - mvv-ui-runner
